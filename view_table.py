#!/usr/bin/env python3
"""
Simple script to view the reviews table from command line.
"""

from database import Database
import pandas as pd


def view_table_simple():
    """View table in simple format."""
    print("=== REVIEWS TABLE ===\n")
    
    with Database("app.db") as db:
        reviews = db.execute_query("SELECT * FROM revs ORDER BY id")
        
        if not reviews:
            print("No reviews found in database.")
            return
        
        print(f"Total reviews: {len(reviews)}\n")
        
        for review in reviews:
            print(f"ID: {review['id']}")
            print(f"Review: {review['reviews']}")
            print(f"Original Agent Reply: {review['agent_reply'] or 'None'}")
            print(f"Edited Agent Reply: {review['edited_reviews'] or 'None'}")
            print(f"Modified: {review['modified_or_not']}")
            print(f"Status: {review['status']}")
            print(f"Created: {review['created_at']}")
            print(f"Updated: {review['updated_at']}")
            print("-" * 80)


def view_table_formatted():
    """View table in formatted pandas DataFrame."""
    print("=== REVIEWS TABLE (FORMATTED) ===\n")
    
    with Database("app.db") as db:
        reviews = db.execute_query("SELECT * FROM revs ORDER BY id")
        
        if not reviews:
            print("No reviews found in database.")
            return
        
        # Convert to DataFrame
        df = pd.DataFrame(reviews)
        
        # Reorder columns
        column_order = ['id', 'reviews', 'agent_reply', 'edited_reviews', 'modified_or_not', 'status', 'created_at', 'updated_at']
        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]
        
        # Truncate long text for better display
        df_display = df.copy()
        text_columns = ['reviews', 'agent_reply', 'edited_reviews']
        for col in text_columns:
            if col in df_display.columns:
                df_display[col] = df_display[col].astype(str).str[:40] + '...'
        
        print(df_display.to_string(index=False))
        print(f"\nTotal records: {len(df)}")


def view_table_csv():
    """Export table to CSV format."""
    print("=== EXPORTING TO CSV ===\n")
    
    with Database("app.db") as db:
        reviews = db.execute_query("SELECT * FROM revs ORDER BY id")
        
        if not reviews:
            print("No reviews found in database.")
            return
        
        # Convert to DataFrame
        df = pd.DataFrame(reviews)
        
        # Save to CSV
        filename = "reviews_export.csv"
        df.to_csv(filename, index=False)
        print(f"Table exported to {filename}")
        print(f"Total records exported: {len(df)}")


def view_specific_columns():
    """View only specific columns."""
    print("=== SPECIFIC COLUMNS VIEW ===\n")
    
    with Database("app.db") as db:
        reviews = db.execute_query("""
            SELECT id, reviews, agent_reply, edited_reviews, modified_or_not, status 
            FROM revs 
            ORDER BY id
        """)
        
        if not reviews:
            print("No reviews found in database.")
            return
        
        print("ID | Review (50 chars) | Original Reply (30 chars) | Edited Reply (30 chars) | Modified | Status")
        print("-" * 120)
        
        for review in reviews:
            review_text = (review['reviews'][:47] + '...') if len(review['reviews']) > 50 else review['reviews']
            original_reply = (review['agent_reply'][:27] + '...') if review['agent_reply'] and len(review['agent_reply']) > 30 else (review['agent_reply'] or 'None')
            edited_reply = (review['edited_reviews'][:27] + '...') if review['edited_reviews'] and len(review['edited_reviews']) > 30 else (review['edited_reviews'] or 'None')
            
            print(f"{review['id']:2} | {review_text:50} | {original_reply:30} | {edited_reply:30} | {review['modified_or_not']:8} | {review['status']}")


def main():
    """Main function with menu."""
    print("Review Table Viewer")
    print("==================")
    print("1. Simple view")
    print("2. Formatted table view")
    print("3. Export to CSV")
    print("4. Specific columns view")
    print("5. All views")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == '1':
        view_table_simple()
    elif choice == '2':
        view_table_formatted()
    elif choice == '3':
        view_table_csv()
    elif choice == '4':
        view_specific_columns()
    elif choice == '5':
        view_table_simple()
        print("\n" + "="*80 + "\n")
        view_table_formatted()
        print("\n" + "="*80 + "\n")
        view_specific_columns()
    else:
        print("Invalid choice. Showing simple view by default.")
        view_table_simple()


if __name__ == "__main__":
    main()
