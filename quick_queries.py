#!/usr/bin/env python3
"""
Quick Query Examples for Reviews Database
Copy and paste these examples to perform common operations.
"""

from database import Database

# Quick examples you can copy and use
def quick_examples():
    """Quick copy-paste examples for common operations."""
    
    with Database("app.db") as db:
        
        print("=== QUICK QUERY EXAMPLES ===\n")
        
        # 1. Add a new review
        print("1. ADD NEW REVIEW:")
        print("db.execute_update(")
        print("    'INSERT INTO revs (reviews, text, agent_reply, modified_or_not, status) VALUES (?, ?, ?, ?, ?)',")
        print("    ('Your review here', 'Additional text', 'Agent response', 'not modified', 'active')")
        print(")")
        
        # Example execution:
        # db.execute_update(
        #     "INSERT INTO revs (reviews, text, agent_reply, modified_or_not, status) VALUES (?, ?, ?, ?, ?)",
        #     ("Great customer service!", "Very helpful staff", "Thank you for your feedback!", "not modified", "active")
        # )
        
        print("\n" + "-"*60 + "\n")
        
        # 2. Get all reviews
        print("2. GET ALL REVIEWS:")
        print("all_reviews = db.execute_query('SELECT * FROM revs ORDER BY created_at DESC')")
        
        all_reviews = db.execute_query("SELECT * FROM revs ORDER BY created_at DESC")
        print(f"Found {len(all_reviews)} reviews")
        
        print("\n" + "-"*60 + "\n")
        
        # 3. Get reviews by status
        print("3. GET REVIEWS BY STATUS:")
        print("active_reviews = db.execute_query('SELECT * FROM revs WHERE status = ?', ('active',))")
        
        active_reviews = db.execute_query("SELECT * FROM revs WHERE status = ?", ("active",))
        print(f"Found {len(active_reviews)} active reviews")
        
        print("\n" + "-"*60 + "\n")
        
        # 4. Search reviews
        print("4. SEARCH REVIEWS:")
        print("search_results = db.execute_query(")
        print("    'SELECT * FROM revs WHERE reviews LIKE ? OR text LIKE ? OR agent_reply LIKE ?',")
        print("    ('%search_term%', '%search_term%', '%search_term%')")
        print(")")
        
        search_results = db.execute_query(
            "SELECT * FROM revs WHERE reviews LIKE ? OR text LIKE ? OR agent_reply LIKE ?",
            ("%product%", "%product%", "%product%")
        )
        print(f"Found {len(search_results)} reviews containing 'product'")
        
        print("\n" + "-"*60 + "\n")
        
        # 5. Update review status
        print("5. UPDATE REVIEW STATUS:")
        print("db.execute_update(")
        print("    'UPDATE revs SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',")
        print("    ('resolved', 1)")
        print(")")
        
        print("\n" + "-"*60 + "\n")
        
        # 6. Get modified reviews
        print("6. GET MODIFIED REVIEWS:")
        print("modified_reviews = db.execute_query('SELECT * FROM revs WHERE modified_or_not = ?', ('modified',))")
        
        modified_reviews = db.execute_query("SELECT * FROM revs WHERE modified_or_not = ?", ("modified",))
        print(f"Found {len(modified_reviews)} modified reviews")
        
        print("\n" + "-"*60 + "\n")
        
        # 7. Get statistics
        print("7. GET STATISTICS:")
        print("# Total count")
        print("total = db.execute_query('SELECT COUNT(*) as count FROM revs')")
        print("# By status")
        print("by_status = db.execute_query('SELECT status, COUNT(*) as count FROM revs GROUP BY status')")
        
        total = db.execute_query("SELECT COUNT(*) as count FROM revs")
        by_status = db.execute_query("SELECT status, COUNT(*) as count FROM revs GROUP BY status")
        
        print(f"Total reviews: {total[0]['count']}")
        print("By status:")
        for status_row in by_status:
            print(f"  {status_row['status']}: {status_row['count']}")
        
        print("\n" + "-"*60 + "\n")
        
        # 8. Recent reviews
        print("8. GET RECENT REVIEWS:")
        print("recent = db.execute_query('SELECT * FROM revs ORDER BY created_at DESC LIMIT ?', (5,))")
        
        recent = db.execute_query("SELECT * FROM revs ORDER BY created_at DESC LIMIT ?", (5,))
        print(f"Found {len(recent)} recent reviews")
        
        print("\n" + "-"*60 + "\n")
        
        # 9. Complex queries
        print("9. COMPLEX QUERIES:")
        print("# Reviews with specific conditions")
        print("complex = db.execute_query('''")
        print("    SELECT id, reviews, status, created_at")
        print("    FROM revs")
        print("    WHERE status IN ('active', 'pending')")
        print("    AND modified_or_not = 'not modified'")
        print("    ORDER BY created_at DESC")
        print("''', ())")
        
        complex = db.execute_query("""
            SELECT id, reviews, status, created_at
            FROM revs
            WHERE status IN ('active', 'pending')
            AND modified_or_not = 'not modified'
            ORDER BY created_at DESC
        """, ())
        print(f"Found {len(complex)} unmodified active/pending reviews")
        
        print("\n" + "-"*60 + "\n")
        
        # 10. Bulk operations
        print("10. BULK OPERATIONS:")
        print("# Update multiple records")
        print("db.execute_update('UPDATE revs SET status = ? WHERE status = ?', ('archived', 'resolved'))")
        print("# Insert multiple records")
        print("data = [('review1', 'text1', 'reply1', 'not modified', 'active'),")
        print("        ('review2', 'text2', 'reply2', 'modified', 'pending')]")
        print("db.execute_many('INSERT INTO revs (reviews, text, agent_reply, modified_or_not, status) VALUES (?, ?, ?, ?, ?)', data)")


def common_filters():
    """Show common filter examples."""
    
    with Database("app.db") as db:
        print("\n=== COMMON FILTER EXAMPLES ===\n")
        
        # Status filters
        statuses = ['active', 'pending', 'resolved', 'in_progress', 'archived']
        for status in statuses:
            count = db.execute_query("SELECT COUNT(*) as count FROM revs WHERE status = ?", (status,))
            if count[0]['count'] > 0:
                print(f"Status '{status}': {count[0]['count']} reviews")
        
        print("\n" + "-"*40 + "\n")
        
        # Date filters
        print("DATE FILTER EXAMPLES:")
        print("# Today's reviews")
        print("today = db.execute_query('SELECT * FROM revs WHERE DATE(created_at) = DATE(\"now\")')")
        
        print("# Last 7 days")
        print("week = db.execute_query('SELECT * FROM revs WHERE created_at >= datetime(\"now\", \"-7 days\")')")
        
        print("# This month")
        print("month = db.execute_query('SELECT * FROM revs WHERE strftime(\"%Y-%m\", created_at) = strftime(\"%Y-%m\", \"now\")')")
        
        # Execute examples
        today = db.execute_query("SELECT * FROM revs WHERE DATE(created_at) = DATE('now')")
        week = db.execute_query("SELECT * FROM revs WHERE created_at >= datetime('now', '-7 days')")
        
        print(f"Today: {len(today)} reviews")
        print(f"Last 7 days: {len(week)} reviews")


if __name__ == "__main__":
    quick_examples()
    common_filters()
    
    print("\n" + "="*60)
    print("COPY-PASTE READY EXAMPLES:")
    print("="*60)
    print("""
# Basic usage pattern:
with Database("app.db") as db:
    # Your queries here
    results = db.execute_query("SELECT * FROM revs WHERE status = ?", ("active",))
    for review in results:
        print(f"Review: {review['reviews']}")

# Add new review:
with Database("app.db") as db:
    db.execute_update(
        "INSERT INTO revs (reviews, text, agent_reply, modified_or_not, status) VALUES (?, ?, ?, ?, ?)",
        ("Your review", "Additional text", "Agent reply", "not modified", "active")
    )

# Update review:
with Database("app.db") as db:
    db.execute_update(
        "UPDATE revs SET status = ? WHERE id = ?",
        ("resolved", 1)
    )

# Search reviews:
with Database("app.db") as db:
    results = db.execute_query(
        "SELECT * FROM revs WHERE reviews LIKE ?",
        ("%search_term%",)
    )
    """)
