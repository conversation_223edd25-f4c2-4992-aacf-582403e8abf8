#!/usr/bin/env python3
"""
Test script to verify the edited_reviews functionality works correctly.
"""

from database import Database

def test_edited_reviews():
    """Test the edited reviews functionality."""
    
    with Database("app.db") as db:
        print("=== Testing Edited Reviews Functionality ===\n")
        
        # 1. Check current reviews
        print("1. Current reviews in database:")
        reviews = db.execute_query("SELECT id, reviews, agent_reply, edited_reviews, modified_or_not FROM revs")
        
        for review in reviews:
            print(f"ID: {review['id']}")
            print(f"Review: {review['reviews'][:50]}...")
            print(f"Original Reply: {review['agent_reply'][:50] if review['agent_reply'] else 'None'}...")
            print(f"Edited Reply: {review['edited_reviews'][:50] if review['edited_reviews'] else 'None'}...")
            print(f"Modified: {review['modified_or_not']}")
            print("-" * 50)
        
        # 2. Test updating a review with edited reply
        if reviews:
            test_review_id = reviews[0]['id']
            print(f"\n2. Testing edit functionality on review ID {test_review_id}")
            
            # Update with edited reply
            new_edited_reply = "This is a test edited reply - updated at test time!"
            affected_rows = db.execute_update(
                "UPDATE revs SET edited_reviews = ?, modified_or_not = 'modified', updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_edited_reply, test_review_id)
            )
            
            if affected_rows > 0:
                print("✅ Successfully updated review with edited reply")
                
                # Verify the update
                updated_review = db.execute_query("SELECT * FROM revs WHERE id = ?", (test_review_id,))
                if updated_review:
                    review = updated_review[0]
                    print(f"Original Reply: {review['agent_reply']}")
                    print(f"Edited Reply: {review['edited_reviews']}")
                    print(f"Modified Status: {review['modified_or_not']}")
                    print(f"Updated At: {review['updated_at']}")
            else:
                print("❌ Failed to update review")
        
        # 3. Show summary
        print(f"\n3. Summary:")
        total_reviews = len(reviews)
        modified_reviews = len([r for r in reviews if r['modified_or_not'] == 'modified'])
        reviews_with_edits = len([r for r in reviews if r['edited_reviews']])
        
        print(f"Total reviews: {total_reviews}")
        print(f"Modified reviews: {modified_reviews}")
        print(f"Reviews with edited replies: {reviews_with_edits}")
        
        # 4. Test query for reviews with both original and edited replies
        print(f"\n4. Reviews with both original and edited replies:")
        both_replies = db.execute_query("""
            SELECT id, reviews, agent_reply, edited_reviews 
            FROM revs 
            WHERE agent_reply IS NOT NULL AND edited_reviews IS NOT NULL
        """)
        
        for review in both_replies:
            print(f"ID {review['id']}: Has both original and edited replies")
            print(f"  Original: {review['agent_reply'][:30]}...")
            print(f"  Edited: {review['edited_reviews'][:30]}...")


if __name__ == "__main__":
    test_edited_reviews()
