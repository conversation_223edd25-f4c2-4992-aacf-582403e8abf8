#!/usr/bin/env python3
"""
Show database table schema.
"""

from database import Database

def show_table_schema():
    """Show the schema of the revs table."""
    with Database("app.db") as db:
        print("=== REVS TABLE SCHEMA ===\n")
        
        schema = db.execute_query("PRAGMA table_info(revs)")
        
        print("Column Name     | Data Type | Nullable | Primary Key")
        print("-" * 55)
        
        for col in schema:
            name = col['name']
            data_type = col['type']
            nullable = "NULL" if not col['notnull'] else "NOT NULL"
            pk = "YES" if col['pk'] else "NO"
            
            print(f"{name:15} | {data_type:9} | {nullable:8} | {pk}")
        
        print(f"\nTotal columns: {len(schema)}")

if __name__ == "__main__":
    show_table_schema()
