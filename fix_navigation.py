#!/usr/bin/env python3
"""
Quick fix for the navigation issue in streamlit_app.py
"""

# Read the current file
with open('streamlit_app.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Fix the corrupted line
old_line = '        ["📊 Dashboard", "📝 Manage Reviews",  "�📈 Statistics"]'
new_line = '        ["📊 Dashboard", "📝 Manage Reviews", "📈 Statistics"]'

# Replace the corrupted line
fixed_content = content.replace(old_line, new_line)

# Also fix any other potential issues
fixed_content = fixed_content.replace('"�📈 Statistics"', '"📈 Statistics"')
fixed_content = fixed_content.replace('�📈', '📈')

# Write the fixed content back
with open('streamlit_app.py', 'w', encoding='utf-8') as f:
    f.write(fixed_content)

print("Fixed navigation in streamlit_app.py")
print("The Statistics tab should now work properly!")
