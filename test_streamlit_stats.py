#!/usr/bin/env python3
"""
Test the statistics functionality that should now work in Streamlit.
"""

import pandas as pd
from database import Database

def simulate_streamlit_stats():
    """Simulate what the Streamlit statistics page should show."""
    
    print("=== SIMULATING STREAMLIT STATISTICS PAGE ===\n")
    
    # Load reviews (same as Streamlit does)
    with Database("app.db") as db:
        reviews = db.execute_query("SELECT * FROM revs ORDER BY created_at DESC")
    
    if not reviews:
        print("❌ No reviews found!")
        return
    
    # Convert to DataFrame (same as Streamlit does)
    df = pd.DataFrame(reviews)
    df['created_at'] = pd.to_datetime(df['created_at'])
    df['updated_at'] = pd.to_datetime(df['updated_at'])
    
    # Calculate KPIs (same as Streamlit does)
    total_reviews = len(df)
    modified_reviews = len(df[df['modified_or_not'] == 'modified'])
    active_reviews = len(df[df['status'] == 'active'])
    pending_reviews = len(df[df['status'] == 'pending'])
    resolved_reviews = len(df[df['status'] == 'resolved'])
    
    # Calculate percentages
    modification_rate = (modified_reviews / total_reviews * 100) if total_reviews > 0 else 0
    resolution_rate = (resolved_reviews / total_reviews * 100) if total_reviews > 0 else 0
    active_rate = (active_reviews / total_reviews * 100) if total_reviews > 0 else 0
    
    # Reviews with edited replies
    reviews_with_edits = len(df[df['edited_reviews'].notna() & (df['edited_reviews'] != '')])
    edit_rate = (reviews_with_edits / total_reviews * 100) if total_reviews > 0 else 0
    
    print("🎯 KEY PERFORMANCE INDICATORS")
    print("=" * 50)
    print(f"📝 Total Reviews: {total_reviews}")
    print(f"✏️ Modification Rate: {modification_rate:.1f}% ({modified_reviews} modified)")
    print(f"✅ Resolution Rate: {resolution_rate:.1f}% ({resolved_reviews} resolved)")
    print(f"🔄 Active Reviews: {active_rate:.1f}% ({active_reviews} active)")
    print(f"📝 Edited Replies: {edit_rate:.1f}% ({reviews_with_edits} edited)")
    
    pending_rate = (pending_reviews / total_reviews * 100) if total_reviews > 0 else 0
    print(f"⏳ Pending Reviews: {pending_rate:.1f}% ({pending_reviews} pending)")
    
    # Average response length
    avg_reply_length = df['agent_reply'].str.len().mean() if not df['agent_reply'].isna().all() else 0
    print(f"📏 Avg Reply Length: {avg_reply_length:.0f} characters")
    
    # Reviews per day
    date_range = (df['created_at'].max() - df['created_at'].min()).days
    reviews_per_day = total_reviews / max(date_range, 1)
    print(f"📅 Reviews/Day: {reviews_per_day:.1f}")
    
    print("\n📊 VISUALIZATIONS DATA")
    print("=" * 50)
    
    # Status distribution
    status_counts = df['status'].value_counts()
    print("Status Distribution:")
    for status, count in status_counts.items():
        percentage = (count / total_reviews * 100)
        print(f"  • {status.title()}: {count} ({percentage:.1f}%)")
    
    # Modification status
    mod_counts = df['modified_or_not'].value_counts()
    print("\nModification Status:")
    for mod_status, count in mod_counts.items():
        percentage = (count / total_reviews * 100)
        print(f"  • {mod_status.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
    
    print("\n📈 RECOMMENDATIONS")
    print("=" * 50)
    
    if modification_rate > 50:
        print("🔴 High modification rate - consider improving initial responses")
    elif modification_rate > 25:
        print("🟡 Moderate modification rate - room for improvement")
    else:
        print("🟢 Low modification rate - good initial response quality")
    
    if resolution_rate > 75:
        print("🟢 High resolution rate - excellent performance")
    elif resolution_rate > 50:
        print("🟡 Moderate resolution rate - consider follow-up strategies")
    else:
        print("🔴 Low resolution rate - needs attention")
    
    if pending_reviews > total_reviews * 0.3:
        print("🔴 High pending reviews - prioritize processing")
    else:
        print("🟢 Manageable pending reviews")
    
    print(f"\n✅ This is what you should see in the Streamlit Statistics tab!")
    print(f"📱 Go to http://localhost:8501 and click '📈 Statistics'")

if __name__ == "__main__":
    simulate_streamlit_stats()
