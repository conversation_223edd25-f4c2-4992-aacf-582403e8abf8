# Python SQLite Database Project

This project provides a simple and clean Python interface for working with SQLite databases.

## Features

- **Database Class**: A wrapper around SQLite with convenient methods
- **Context Manager Support**: Automatic connection management
- **Type Hints**: Full type annotations for better IDE support
- **Error Handling**: Proper exception handling and rollback support
- **Sample Data**: Example tables and data for testing

## Files

- `database.py` - Main database class and functionality
- `example_usage.py` - Examples of how to use the database
- `requirements.txt` - Python dependencies
- `README.md` - This file

## Quick Start

1. **Install dependencies** (optional, sqlite3 is built into Python):
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the basic example**:
   ```bash
   python database.py
   ```

3. **Run advanced examples**:
   ```bash
   python example_usage.py
   ```

## Database Schema

The example includes two tables:

### Users Table
- `id` (INTEGER PRIMARY KEY) - Auto-incrementing user ID
- `username` (TEXT UNIQUE) - Unique username
- `email` (TEXT UNIQUE) - Unique email address
- `created_at` (TIMESTAMP) - When the user was created
- `updated_at` (TIMESTAMP) - When the user was last updated

### Posts Table
- `id` (INTEGER PRIMARY KEY) - Auto-incrementing post ID
- `user_id` (INTEGER) - Foreign key to users table
- `title` (TEXT) - Post title
- `content` (TEXT) - Post content
- `created_at` (TIMESTAMP) - When the post was created
- `updated_at` (TIMESTAMP) - When the post was last updated

## Usage Examples

### Basic Usage

```python
from database import Database

# Using context manager (recommended)
with Database("my_app.db") as db:
    # Create tables
    db.create_tables()
    
    # Insert data
    db.execute_update(
        "INSERT INTO users (username, email) VALUES (?, ?)",
        ("john_doe", "<EMAIL>")
    )
    
    # Query data
    users = db.execute_query("SELECT * FROM users")
    for user in users:
        print(f"User: {user['username']}")
```

### Manual Connection Management

```python
from database import Database

db = Database("my_app.db")
try:
    db.connect()
    db.create_tables()
    # ... your database operations
finally:
    db.disconnect()
```

### Batch Operations

```python
with Database("my_app.db") as db:
    # Insert multiple records at once
    users_data = [
        ("user1", "<EMAIL>"),
        ("user2", "<EMAIL>"),
        ("user3", "<EMAIL>")
    ]
    
    db.execute_many(
        "INSERT INTO users (username, email) VALUES (?, ?)",
        users_data
    )
```

## Available Methods

### Database Class Methods

- `connect()` - Establish database connection
- `disconnect()` - Close database connection
- `execute_query(query, params)` - Execute SELECT queries
- `execute_update(query, params)` - Execute INSERT/UPDATE/DELETE queries
- `execute_many(query, params_list)` - Execute batch operations
- `create_tables()` - Create the example tables

### Context Manager

The Database class supports Python's `with` statement for automatic connection management:

```python
with Database("app.db") as db:
    # Connection is automatically opened
    db.create_tables()
    # Connection is automatically closed when exiting the block
```

## Error Handling

The database class includes proper error handling:

- Automatic rollback on failed transactions
- Descriptive error messages
- Proper exception propagation

## Customization

You can easily customize the database schema by modifying the `create_tables()` method in `database.py` or by creating your own table creation methods.

## Database Files

- `app.db` - Created when running `database.py`
- `example.db` - Created when running basic examples
- `advanced_example.db` - Created when running advanced examples

These SQLite database files will be created in the same directory as your Python scripts.

## Next Steps

- Modify the schema to fit your needs
- Add more complex queries and operations
- Implement data validation
- Add logging functionality
- Create a web API using Flask or FastAPI
