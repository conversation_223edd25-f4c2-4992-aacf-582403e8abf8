#!/usr/bin/env python3
"""
Example usage of the Database class.
This script demonstrates various database operations.
"""

from database import Database


def main():
    """Main function demonstrating database usage."""
    
    # Using context manager (recommended)
    print("=== Using Database with Context Manager ===")
    with Database("example.db") as db:
        # Create tables
        db.create_tables()
        
        # Insert a new user
        print("\n1. Inserting a new user...")
        user_id = db.execute_update(
            "INSERT INTO users (username, email) VALUES (?, ?)",
            ("alice_wonder", "<EMAIL>")
        )
        print(f"Inserted user, affected rows: {user_id}")
        
        # Insert a post for the user
        print("\n2. Inserting a post...")
        # First, get the user ID
        user_result = db.execute_query(
            "SELECT id FROM users WHERE username = ?",
            ("alice_wonder",)
        )
        if user_result:
            user_id = user_result[0]['id']
            post_id = db.execute_update(
                "INSERT INTO posts (user_id, title, content) VALUES (?, ?, ?)",
                (user_id, "Welcome to Wonderland", "Down the rabbit hole we go!")
            )
            print(f"Inserted post, affected rows: {post_id}")
        
        # Query all users
        print("\n3. Querying all users...")
        users = db.execute_query("SELECT * FROM users")
        for user in users:
            print(f"  User: {user['username']} ({user['email']})")
        
        # Query posts with user information
        print("\n4. Querying posts with user info...")
        posts_query = """
        SELECT p.title, p.content, u.username, p.created_at
        FROM posts p
        JOIN users u ON p.user_id = u.id
        ORDER BY p.created_at DESC
        """
        posts = db.execute_query(posts_query)
        for post in posts:
            print(f"  '{post['title']}' by {post['username']}")
            print(f"    Content: {post['content']}")
            print(f"    Created: {post['created_at']}")
        
        # Update a user's email
        print("\n5. Updating user email...")
        updated_rows = db.execute_update(
            "UPDATE users SET email = ? WHERE username = ?",
            ("<EMAIL>", "alice_wonder")
        )
        print(f"Updated {updated_rows} user(s)")
        
        # Verify the update
        updated_user = db.execute_query(
            "SELECT * FROM users WHERE username = ?",
            ("alice_wonder",)
        )
        if updated_user:
            print(f"  Updated user email: {updated_user[0]['email']}")
        
        # Count records
        print("\n6. Counting records...")
        user_count = db.execute_query("SELECT COUNT(*) as count FROM users")
        post_count = db.execute_query("SELECT COUNT(*) as count FROM posts")
        print(f"  Total users: {user_count[0]['count']}")
        print(f"  Total posts: {post_count[0]['count']}")


def advanced_example():
    """Advanced example with batch operations."""
    print("\n\n=== Advanced Database Operations ===")
    
    with Database("advanced_example.db") as db:
        db.create_tables()
        
        # Batch insert multiple users
        print("\n1. Batch inserting users...")
        users_data = [
            ("user1", "<EMAIL>"),
            ("user2", "<EMAIL>"),
            ("user3", "<EMAIL>"),
            ("user4", "<EMAIL>"),
            ("user5", "<EMAIL>")
        ]
        
        affected_rows = db.execute_many(
            "INSERT OR IGNORE INTO users (username, email) VALUES (?, ?)",
            users_data
        )
        print(f"Batch inserted {affected_rows} users")
        
        # Complex query with aggregation
        print("\n2. Complex query - Users with post counts...")
        complex_query = """
        SELECT 
            u.id,
            u.username,
            u.email,
            COUNT(p.id) as post_count,
            MAX(p.created_at) as last_post_date
        FROM users u
        LEFT JOIN posts p ON u.id = p.user_id
        GROUP BY u.id, u.username, u.email
        ORDER BY post_count DESC, u.username
        """
        
        results = db.execute_query(complex_query)
        for result in results:
            print(f"  {result['username']}: {result['post_count']} posts")
            if result['last_post_date']:
                print(f"    Last post: {result['last_post_date']}")
        
        # Search functionality
        print("\n3. Search functionality...")
        search_term = "example"
        search_query = """
        SELECT u.username, u.email
        FROM users u
        WHERE u.username LIKE ? OR u.email LIKE ?
        """
        search_results = db.execute_query(
            search_query, 
            (f"%{search_term}%", f"%{search_term}%")
        )
        
        print(f"Users matching '{search_term}':")
        for result in search_results:
            print(f"  {result['username']} - {result['email']}")


if __name__ == "__main__":
    main()
    advanced_example()
    print("\n=== Database operations completed successfully! ===")
