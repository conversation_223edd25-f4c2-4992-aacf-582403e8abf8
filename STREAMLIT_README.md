# 📝 Review Management System - Streamlit Frontend

A modern web interface for managing customer reviews with agent replies and status tracking.

## 🚀 Features

### 📊 Dashboard
- **Overview metrics**: Total, active, pending, and modified reviews
- **Recent reviews**: Quick view of the latest 5 reviews
- **Status indicators**: Color-coded status and modification flags

### 📝 Manage Reviews
- **View all reviews** with filtering options
- **Edit agent replies** in real-time
- **Update review status** (active, pending, resolved, in_progress, archived)
- **Filter by status** and modification state
- **Automatic database updates** when replies are edited

### ➕ Add New Review
- **Add new reviews** with customer feedback
- **Set initial agent replies**
- **Choose initial status**
- **Form validation** to ensure required fields

### 📈 Statistics
- **Comprehensive statistics** with charts
- **Status distribution** visualization
- **Modification tracking** charts
- **Real-time data** from your SQLite database

## 🛠️ How to Run

1. **Install dependencies:**
   ```bash
   pip install streamlit pandas
   ```

2. **Run the application:**
   ```bash
   python -m streamlit run streamlit_app.py
   ```

3. **Open in browser:**
   - Local URL: http://localhost:8501
   - The app will automatically open in your default browser

## 💾 Database Integration

- **Backend**: SQLite database (`app.db`)
- **Real-time updates**: Changes are immediately saved to database
- **Automatic table creation**: Database and tables are created if they don't exist
- **Sample data**: Includes sample reviews for testing

## 🎯 Key Functionality

### Edit Agent Replies
1. Go to "📝 Manage Reviews" page
2. Expand any review
3. Edit the agent reply in the text area
4. Click "Update Reply" button
5. **Automatic marking**: Review is automatically marked as "modified"
6. **Database update**: Changes are saved immediately

### Update Review Status
1. Select new status from dropdown
2. Click "Update Status" button
3. Status is updated in database with timestamp

### Filter and Search
- Filter by status (All, active, pending, resolved, etc.)
- Filter by modification state (All, modified, not modified)
- Real-time filtering without page refresh

## 📱 User Interface

### Navigation
- **Sidebar navigation** with 4 main pages
- **Responsive design** that works on desktop and mobile
- **Clean, modern interface** with color-coded elements

### Visual Elements
- 🟢 Active reviews
- 🟡 Pending reviews  
- 🔵 In-progress reviews
- ⚫ Resolved reviews
- ✅ Modified reviews
- ❌ Unmodified reviews

## 🔧 Technical Details

### File Structure
```
streamlit_app.py          # Main Streamlit application
database.py               # Database connection and operations
revs_operations.py        # Review-specific database functions
app.db                    # SQLite database file
```

### Database Schema
```sql
CREATE TABLE revs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    reviews TEXT,                    -- Customer review content
    text TEXT,                       -- Additional context
    agent_reply TEXT,                -- Agent's response
    modified_or_not TEXT,            -- 'modified' or 'not modified'
    status TEXT,                     -- active, pending, resolved, etc.
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🎨 Customization

### Adding New Status Options
Edit the status options in `streamlit_app.py`:
```python
status_options = ["active", "pending", "resolved", "in_progress", "archived", "your_new_status"]
```

### Styling
The app includes custom CSS for:
- Color-coded status indicators
- Responsive layout
- Modern card-based design
- Professional typography

## 🚨 Important Notes

1. **Auto-save**: All changes are automatically saved to the database
2. **Real-time updates**: Use `st.rerun()` to refresh data after updates
3. **Data persistence**: All data is stored in SQLite database
4. **Backup**: Consider backing up your `app.db` file regularly

## 🔄 Workflow

### Typical Review Management Workflow:
1. **View Dashboard** - Get overview of all reviews
2. **Manage Reviews** - Edit agent replies and update statuses
3. **Add New Reviews** - Input new customer feedback
4. **Check Statistics** - Monitor performance and trends

### When Agent Reply is Edited:
1. User edits reply in text area
2. Clicks "Update Reply" button
3. Database is updated with new reply
4. `modified_or_not` field is set to "modified"
5. `updated_at` timestamp is updated
6. Page refreshes to show changes

## 🎯 Next Steps

- Add user authentication
- Implement review categories
- Add email notifications
- Export functionality
- Advanced search and filtering
- Review analytics and reporting

## 🐛 Troubleshooting

**If the app doesn't start:**
- Make sure all dependencies are installed
- Check that port 8501 is available
- Verify database permissions

**If changes don't save:**
- Check database file permissions
- Verify database connection
- Look for error messages in the terminal

**For development:**
- Use `st.write()` for debugging
- Check browser console for JavaScript errors
- Monitor terminal output for Python errors
