#!/usr/bin/env python3
"""
Comprehensive operations for the 'revs' table.
This script demonstrates various queries and operations you can perform.
"""

from database import Database
from typing import List, Dict, Any


def add_new_review(db: Database, reviews: str, text: str, agent_reply: str, 
                   modified_or_not: str, status: str) -> int:
    """Add a new review to the database."""
    query = """
    INSERT INTO revs (reviews, text, agent_reply, modified_or_not, status) 
    VALUES (?, ?, ?, ?, ?)
    """
    affected_rows = db.execute_update(query, (reviews, text, agent_reply, modified_or_not, status))
    print(f"Added new review. Affected rows: {affected_rows}")
    return affected_rows


def get_all_reviews(db: Database) -> List[Dict[str, Any]]:
    """Get all reviews from the database."""
    return db.execute_query("SELECT * FROM revs ORDER BY created_at DESC")


def get_reviews_by_status(db: Database, status: str) -> List[Dict[str, Any]]:
    """Get reviews filtered by status."""
    return db.execute_query("SELECT * FROM revs WHERE status = ? ORDER BY created_at DESC", (status,))


def get_modified_reviews(db: Database) -> List[Dict[str, Any]]:
    """Get all reviews that have been modified."""
    return db.execute_query("SELECT * FROM revs WHERE modified_or_not = 'modified' ORDER BY created_at DESC")


def get_unmodified_reviews(db: Database) -> List[Dict[str, Any]]:
    """Get all reviews that have not been modified."""
    return db.execute_query("SELECT * FROM revs WHERE modified_or_not = 'not modified' ORDER BY created_at DESC")


def search_reviews(db: Database, search_term: str) -> List[Dict[str, Any]]:
    """Search reviews by content in reviews, text, or agent_reply fields."""
    query = """
    SELECT * FROM revs 
    WHERE reviews LIKE ? OR text LIKE ? OR agent_reply LIKE ?
    ORDER BY created_at DESC
    """
    search_pattern = f"%{search_term}%"
    return db.execute_query(query, (search_pattern, search_pattern, search_pattern))


def update_review_status(db: Database, review_id: int, new_status: str) -> int:
    """Update the status of a specific review."""
    query = "UPDATE revs SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    affected_rows = db.execute_update(query, (new_status, review_id))
    print(f"Updated review ID {review_id} status to '{new_status}'. Affected rows: {affected_rows}")
    return affected_rows


def update_agent_reply(db: Database, review_id: int, new_reply: str) -> int:
    """Update the agent reply for a specific review."""
    query = "UPDATE revs SET agent_reply = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    affected_rows = db.execute_update(query, (new_reply, review_id))
    print(f"Updated agent reply for review ID {review_id}. Affected rows: {affected_rows}")
    return affected_rows


def mark_as_modified(db: Database, review_id: int) -> int:
    """Mark a review as modified."""
    query = "UPDATE revs SET modified_or_not = 'modified', updated_at = CURRENT_TIMESTAMP WHERE id = ?"
    affected_rows = db.execute_update(query, (review_id,))
    print(f"Marked review ID {review_id} as modified. Affected rows: {affected_rows}")
    return affected_rows


def get_review_statistics(db: Database) -> Dict[str, int]:
    """Get statistics about reviews."""
    stats = {}
    
    # Total reviews
    total = db.execute_query("SELECT COUNT(*) as count FROM revs")
    stats['total_reviews'] = total[0]['count']
    
    # Reviews by status
    statuses = db.execute_query("SELECT status, COUNT(*) as count FROM revs GROUP BY status")
    for status_row in statuses:
        stats[f"status_{status_row['status']}"] = status_row['count']
    
    # Modified vs unmodified
    modified = db.execute_query("SELECT modified_or_not, COUNT(*) as count FROM revs GROUP BY modified_or_not")
    for mod_row in modified:
        key = "modified" if mod_row['modified_or_not'] == 'modified' else "unmodified"
        stats[key] = mod_row['count']
    
    return stats


def get_recent_reviews(db: Database, limit: int = 5) -> List[Dict[str, Any]]:
    """Get the most recent reviews."""
    query = "SELECT * FROM revs ORDER BY created_at DESC LIMIT ?"
    return db.execute_query(query, (limit,))


def delete_review(db: Database, review_id: int) -> int:
    """Delete a specific review (use with caution!)."""
    query = "DELETE FROM revs WHERE id = ?"
    affected_rows = db.execute_update(query, (review_id,))
    print(f"Deleted review ID {review_id}. Affected rows: {affected_rows}")
    return affected_rows


def bulk_update_status(db: Database, old_status: str, new_status: str) -> int:
    """Update all reviews with a specific status to a new status."""
    query = "UPDATE revs SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE status = ?"
    affected_rows = db.execute_update(query, (new_status, old_status))
    print(f"Updated all '{old_status}' reviews to '{new_status}'. Affected rows: {affected_rows}")
    return affected_rows


def print_reviews(reviews: List[Dict[str, Any]], title: str = "Reviews") -> None:
    """Helper function to print reviews in a formatted way."""
    print(f"\n=== {title} ===")
    if not reviews:
        print("No reviews found.")
        return
    
    for review in reviews:
        print(f"ID: {review['id']}")
        print(f"Review: {review['reviews']}")
        print(f"Text: {review['text']}")
        print(f"Agent Reply: {review['agent_reply']}")
        print(f"Modified: {review['modified_or_not']}")
        print(f"Status: {review['status']}")
        print(f"Created: {review['created_at']}")
        if review['updated_at'] != review['created_at']:
            print(f"Updated: {review['updated_at']}")
        print("-" * 60)


def main():
    """Main function demonstrating various operations."""
    
    with Database("app.db") as db:
        print("=== Reviews Database Operations Demo ===")
        
        # 1. Add a new review
        print("\n1. Adding a new review...")
        add_new_review(
            db, 
            "Product arrived damaged", 
            "Customer received broken item", 
            "We sincerely apologize and will send a replacement immediately", 
            "not modified", 
            "pending"
        )
        
        # 2. Get all reviews
        print("\n2. All reviews:")
        all_reviews = get_all_reviews(db)
        print(f"Total reviews: {len(all_reviews)}")
        
        # 3. Get reviews by status
        print("\n3. Active reviews:")
        active_reviews = get_reviews_by_status(db, "active")
        print_reviews(active_reviews, "Active Reviews")
        
        print("\n4. Pending reviews:")
        pending_reviews = get_reviews_by_status(db, "pending")
        print_reviews(pending_reviews, "Pending Reviews")
        
        # 5. Get modified reviews
        print("\n5. Modified reviews:")
        modified_reviews = get_modified_reviews(db)
        print_reviews(modified_reviews, "Modified Reviews")
        
        # 6. Search functionality
        print("\n6. Searching for 'product':")
        search_results = search_reviews(db, "product")
        print_reviews(search_results, "Search Results for 'product'")
        
        # 7. Update operations
        print("\n7. Updating review status...")
        if pending_reviews:
            first_pending_id = pending_reviews[0]['id']
            update_review_status(db, first_pending_id, "in_progress")
        
        # 8. Statistics
        print("\n8. Review statistics:")
        stats = get_review_statistics(db)
        for key, value in stats.items():
            print(f"  {key.replace('_', ' ').title()}: {value}")
        
        # 9. Recent reviews
        print("\n9. Most recent 3 reviews:")
        recent = get_recent_reviews(db, 3)
        print_reviews(recent, "Recent Reviews")
        
        # 10. Bulk operations example
        print("\n10. Bulk update example (if any 'resolved' status exists):")
        resolved_count = len(get_reviews_by_status(db, "resolved"))
        if resolved_count > 0:
            print(f"Found {resolved_count} resolved reviews")
            # Uncomment the next line if you want to actually perform the bulk update
            # bulk_update_status(db, "resolved", "archived")
        else:
            print("No resolved reviews found for bulk update demo")


if __name__ == "__main__":
    main()
