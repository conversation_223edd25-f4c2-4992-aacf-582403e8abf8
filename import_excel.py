#!/usr/bin/env python3
"""
Command-line tool to import Excel data into the reviews database.
"""

import pandas as pd
import sys
import os
from database import Database


def validate_excel_data(df):
    """Validate the Excel data format."""
    
    print("🔍 Validating Excel data...")
    
    required_columns = ['reviews', 'agent_reply', 'status']
    optional_columns = ['text', 'edited_reviews', 'modified_or_not']
    
    # Check required columns
    missing_required = [col for col in required_columns if col not in df.columns]
    if missing_required:
        print(f"❌ Missing required columns: {missing_required}")
        return False
    
    # Check for empty required fields
    validation_issues = []
    
    for col in required_columns:
        empty_count = df[col].isna().sum()
        if empty_count > 0:
            validation_issues.append(f"Column '{col}' has {empty_count} empty values")
    
    # Check status values
    valid_statuses = ['active', 'pending', 'resolved', 'in_progress', 'archived']
    invalid_statuses = df[~df['status'].isin(valid_statuses)]['status'].unique()
    if len(invalid_statuses) > 0:
        validation_issues.append(f"Invalid status values found: {list(invalid_statuses)}")
    
    # Check modified_or_not values if present
    if 'modified_or_not' in df.columns:
        valid_modified = ['modified', 'not modified']
        invalid_modified = df[~df['modified_or_not'].isin(valid_modified)]['modified_or_not'].unique()
        if len(invalid_modified) > 0:
            validation_issues.append(f"Invalid modification values found: {list(invalid_modified)}")
    
    if validation_issues:
        print("⚠️ Data validation issues found:")
        for issue in validation_issues:
            print(f"  • {issue}")
        
        fix = input("🔧 Attempt to fix data automatically? (y/n): ").lower().strip()
        if fix == 'y':
            # Fill missing values
            df['text'] = df['text'].fillna('') if 'text' in df.columns else ''
            df['edited_reviews'] = df['edited_reviews'].fillna('') if 'edited_reviews' in df.columns else ''
            if 'modified_or_not' not in df.columns:
                df['modified_or_not'] = 'not modified'
            df['modified_or_not'] = df['modified_or_not'].fillna('not modified')
            
            # Fix invalid statuses
            df.loc[~df['status'].isin(valid_statuses), 'status'] = 'pending'
            
            print("✅ Data automatically fixed!")
            return True
        else:
            return False
    else:
        print("✅ Data validation passed!")
        return True


def import_excel_data(file_path, replace_existing=False):
    """Import data from Excel file."""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        print(f"📖 Reading Excel file: {file_path}")
        df = pd.read_excel(file_path)
        print(f"✅ Found {len(df)} rows in Excel file")
        
        # Validate data
        if not validate_excel_data(df):
            print("❌ Data validation failed. Import cancelled.")
            return False
        
        # Import data
        print("🚀 Starting import...")
        
        with Database("app.db") as db:
            
            if replace_existing:
                print("🗑️ Clearing existing data...")
                db.execute_update("DELETE FROM revs")
            
            success_count = 0
            error_count = 0
            
            for index, row in df.iterrows():
                try:
                    # Prepare values
                    reviews = row['reviews']
                    text = row.get('text', '')
                    agent_reply = row['agent_reply']
                    edited_reviews = row.get('edited_reviews', None)
                    if edited_reviews == '':
                        edited_reviews = None
                    modified_or_not = row.get('modified_or_not', 'not modified')
                    status = row['status']
                    
                    # Insert into database
                    db.execute_update("""
                        INSERT INTO revs (reviews, text, agent_reply, edited_reviews, modified_or_not, status)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (reviews, text, agent_reply, edited_reviews, modified_or_not, status))
                    
                    success_count += 1
                    
                    if success_count % 10 == 0:
                        print(f"  📝 Imported {success_count} reviews...")
                    
                except Exception as e:
                    error_count += 1
                    print(f"❌ Error importing row {index + 1}: {e}")
            
            # Show results
            print(f"\n📊 Import Results:")
            print(f"  ✅ Successfully imported: {success_count} reviews")
            if error_count > 0:
                print(f"  ❌ Failed to import: {error_count} reviews")
            
            # Show updated statistics
            total_reviews = db.execute_query("SELECT COUNT(*) as count FROM revs")[0]['count']
            print(f"  📈 Total reviews in database: {total_reviews}")
            
            return success_count > 0
    
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        return False


def create_sample_excel():
    """Create a sample Excel file for reference."""
    
    sample_data = {
        'reviews': [
            'Great product, very satisfied!',
            'Could use some improvements',
            'Excellent customer service',
            'Product arrived damaged',
            'Fast shipping, good quality'
        ],
        'text': [
            'Customer loves the quality and features',
            'Customer suggests better packaging',
            'Customer praises support team response',
            'Customer received broken item',
            'Customer appreciates quick delivery'
        ],
        'agent_reply': [
            'Thank you for your positive feedback!',
            'We appreciate your suggestions for improvement',
            'We\'re glad our team could help you',
            'We sincerely apologize for the damaged item',
            'Thank you for choosing our service'
        ],
        'edited_reviews': [
            '',
            'We appreciate your suggestions and will improve our packaging',
            '',
            'We sincerely apologize and will send a replacement immediately',
            ''
        ],
        'modified_or_not': [
            'not modified',
            'modified',
            'not modified',
            'modified',
            'not modified'
        ],
        'status': [
            'active',
            'pending',
            'resolved',
            'in_progress',
            'active'
        ]
    }
    
    df = pd.DataFrame(sample_data)
    filename = 'sample_reviews.xlsx'
    df.to_excel(filename, index=False)
    print(f"✅ Sample Excel file created: {filename}")
    return filename


def main():
    """Main function with command-line interface."""
    
    print("📁 Excel Import Tool for Review Management System")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        # Command line mode
        file_path = sys.argv[1]
        replace_mode = '--replace' in sys.argv
        
        if replace_mode:
            print("⚠️ Replace mode enabled - existing data will be deleted!")
        
        success = import_excel_data(file_path, replace_existing=replace_mode)
        if success:
            print("🎉 Import completed successfully!")
        else:
            print("💥 Import failed!")
            sys.exit(1)
    
    else:
        # Interactive mode
        print("Choose an option:")
        print("1. Import Excel file")
        print("2. Create sample Excel file")
        print("3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == '1':
            file_path = input("Enter Excel file path: ").strip()
            
            if not file_path:
                print("❌ No file path provided")
                return
            
            print("\nImport mode:")
            print("1. Add to existing data")
            print("2. Replace all existing data")
            
            mode_choice = input("Choose mode (1-2): ").strip()
            replace_existing = mode_choice == '2'
            
            if replace_existing:
                confirm = input("⚠️ This will delete all existing data. Continue? (y/n): ").lower().strip()
                if confirm != 'y':
                    print("Import cancelled.")
                    return
            
            success = import_excel_data(file_path, replace_existing=replace_existing)
            if success:
                print("🎉 Import completed successfully!")
            else:
                print("💥 Import failed!")
        
        elif choice == '2':
            filename = create_sample_excel()
            print(f"You can now edit {filename} and import it using option 1")
        
        elif choice == '3':
            print("Goodbye!")
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
