#!/usr/bin/env python3
"""
Add more sample data to better demonstrate the KPIs and visualizations.
"""

from database import Database
from datetime import datetime, timedelta
import random

def add_sample_data():
    """Add diverse sample data for better visualization."""
    
    sample_reviews = [
        # Active reviews
        ("Amazing customer service!", "The support team was very helpful", "Thank you for your positive feedback!", None, "not modified", "active"),
        ("Fast delivery and great quality", "Product arrived quickly and in perfect condition", "We're glad you're satisfied with our service!", None, "not modified", "active"),
        ("Love this product!", "Exactly what I was looking for", "Thank you for choosing us!", None, "not modified", "active"),
        
        # Pending reviews
        ("Website is confusing", "Hard to navigate and find products", "We appreciate your feedback and are working on improvements", "We appreciate your feedback and will improve our website navigation soon", "modified", "pending"),
        ("Shipping took too long", "Expected faster delivery", "We apologize for the delay", "We sincerely apologize for the shipping delay and have expedited your order", "modified", "pending"),
        
        # Resolved reviews
        ("Issue with my order", "Received wrong item", "We've sent the correct item", "We've sent the correct item and provided a discount for the inconvenience", "modified", "resolved"),
        ("Product was damaged", "Box was crushed during shipping", "Replacement sent immediately", None, "not modified", "resolved"),
        ("Great resolution!", "Problem was fixed quickly", "Thank you for your patience!", None, "not modified", "resolved"),
        
        # In progress reviews
        ("Need help with setup", "Product setup is complicated", "Our tech team will contact you", "Our technical support team will contact you within 24 hours", "modified", "in_progress"),
        ("Question about warranty", "How long is the warranty period?", "Warranty is 2 years from purchase", None, "not modified", "in_progress"),
        
        # More diverse examples
        ("Excellent value for money", "Great product at a reasonable price", "We strive to offer the best value!", None, "not modified", "active"),
        ("Customer service could improve", "Long wait times on phone", "We're hiring more support staff", "We're expanding our support team to reduce wait times", "modified", "pending"),
        ("Perfect for my needs", "Exactly what I was looking for", "Wonderful to hear!", None, "not modified", "resolved"),
        ("Packaging was poor", "Items were loose in the box", "We'll improve our packaging", "We've updated our packaging standards to prevent this issue", "modified", "resolved"),
        ("Quick response time", "Got help within minutes", "Our team is always ready to help!", None, "not modified", "active"),
    ]
    
    with Database("app.db") as db:
        print("Adding sample data for better visualizations...")
        
        # Check current count
        current_count = db.execute_query("SELECT COUNT(*) as count FROM revs")[0]['count']
        print(f"Current reviews in database: {current_count}")
        
        # Add sample data with varied timestamps
        base_date = datetime.now() - timedelta(days=30)
        
        for i, (reviews, text, agent_reply, edited_reviews, modified_or_not, status) in enumerate(sample_reviews):
            # Create varied timestamps over the last 30 days
            days_offset = random.randint(0, 30)
            hours_offset = random.randint(0, 23)
            created_time = base_date + timedelta(days=days_offset, hours=hours_offset)
            
            # Updated time is either same as created or later
            if modified_or_not == "modified":
                updated_time = created_time + timedelta(hours=random.randint(1, 48))
            else:
                updated_time = created_time
            
            # Insert with custom timestamps
            db.execute_update("""
                INSERT INTO revs (reviews, text, agent_reply, edited_reviews, modified_or_not, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (reviews, text, agent_reply, edited_reviews, modified_or_not, status, 
                  created_time.strftime('%Y-%m-%d %H:%M:%S'), 
                  updated_time.strftime('%Y-%m-%d %H:%M:%S')))
        
        # Check final count
        final_count = db.execute_query("SELECT COUNT(*) as count FROM revs")[0]['count']
        print(f"Added {final_count - current_count} new reviews")
        print(f"Total reviews now: {final_count}")
        
        # Show status breakdown
        print("\nStatus breakdown:")
        statuses = db.execute_query("SELECT status, COUNT(*) as count FROM revs GROUP BY status ORDER BY count DESC")
        for status_row in statuses:
            print(f"  {status_row['status']}: {status_row['count']}")
        
        # Show modification breakdown
        print("\nModification breakdown:")
        modifications = db.execute_query("SELECT modified_or_not, COUNT(*) as count FROM revs GROUP BY modified_or_not")
        for mod_row in modifications:
            print(f"  {mod_row['modified_or_not']}: {mod_row['count']}")

if __name__ == "__main__":
    add_sample_data()
