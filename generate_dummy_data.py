#!/usr/bin/env python3
"""
Generate dummy data with secondary classes and ratings.
"""

from database import Database, add_new_columns
from datetime import datetime, timedelta
import random

def generate_dummy_data():
    """Generate dummy data with the specified secondary classes."""
    
    # Secondary classes as specified
    secondary_classes = [
        "Spin & Win",
        "General App Feedback & App Praise", 
        "Technical Issue & Complaints",
        "Doubts",
        "Suggestions",
        "Service",
        "Staff"
    ]
    
    # Sample reviews for each category
    sample_data = {
        "Spin & Win": [
            ("Love the spin and win feature!", "Great to hear you enjoy it!", 4.5),
            ("Spin wheel is not working properly", "We're looking into this issue", 2.0),
            ("Won amazing prizes from spin wheel", "Congratulations on your wins!", 5.0),
            ("Spin feature could be improved", "Thanks for the feedback", 3.0),
            ("Daily spin rewards are awesome", "We're glad you like them!", 4.0)
        ],
        "General App Feedback & App Praise": [
            ("Amazing app, love using it!", "Thank you for your kind words!", 5.0),
            ("App is very user-friendly", "We appreciate your feedback!", 4.5),
            ("Great experience overall", "Thanks for sharing your experience!", 4.0),
            ("App has improved a lot", "We're constantly working on improvements!", 4.5),
            ("Fantastic features and design", "Thank you for the praise!", 5.0)
        ],
        "Technical Issue & Complaints": [
            ("App keeps crashing on my phone", "We're working on fixing this issue", 1.5),
            ("Login issues persist", "Please contact our technical support", 2.0),
            ("Payment gateway not working", "We're resolving payment issues", 1.0),
            ("App is very slow", "We're optimizing app performance", 2.5),
            ("Features not loading properly", "Technical team is investigating", 2.0)
        ],
        "Doubts": [
            ("How do I redeem my points?", "You can redeem points in the rewards section", 3.0),
            ("Is my data secure?", "Yes, we use advanced security measures", 3.5),
            ("What are the terms and conditions?", "Please check our T&C section", 3.0),
            ("How to contact customer support?", "Use the help section in the app", 3.5),
            ("When will new features be added?", "We regularly update with new features", 3.0)
        ],
        "Suggestions": [
            ("Add dark mode please", "Great suggestion! We'll consider it", 4.0),
            ("Need more payment options", "We're working on adding more options", 3.5),
            ("Add notification settings", "Thanks for the suggestion!", 3.5),
            ("Improve search functionality", "We'll work on search improvements", 3.0),
            ("Add social sharing features", "Interesting idea, we'll consider it", 4.0)
        ],
        "Service": [
            ("Excellent customer service", "Thank you for the positive feedback!", 5.0),
            ("Quick response from support team", "We strive to respond quickly!", 4.5),
            ("Service could be better", "We're working on improving our service", 3.0),
            ("Very helpful support staff", "Our team is always here to help!", 4.5),
            ("Service is outstanding", "We appreciate your kind words!", 5.0)
        ],
        "Staff": [
            ("Staff is very professional", "Thank you for recognizing our team!", 4.5),
            ("Friendly and helpful staff", "Our staff loves helping customers!", 4.5),
            ("Staff needs better training", "We'll work on staff training", 3.0),
            ("Excellent staff behavior", "We're proud of our team!", 5.0),
            ("Staff resolved my issue quickly", "Great to hear about the quick resolution!", 4.0)
        ]
    }
    
    with Database("app.db") as db:
        print("Adding new columns to database...")
        add_new_columns(db)
        
        print("Generating dummy data with secondary classes...")
        
        # Clear existing data first
        clear_existing = input("Clear existing data? (y/n): ").lower().strip()
        if clear_existing == 'y':
            db.execute_update("DELETE FROM revs")
            print("Existing data cleared")
        
        success_count = 0
        base_date = datetime.now() - timedelta(days=30)
        
        for secondary_class, reviews_data in sample_data.items():
            for review_text, agent_reply, rating in reviews_data:
                try:
                    # Create varied timestamps
                    days_offset = random.randint(0, 30)
                    hours_offset = random.randint(0, 23)
                    created_time = base_date + timedelta(days=days_offset, hours=hours_offset)
                    
                    # Random status
                    status = random.choice(['active', 'pending', 'resolved', 'in_progress'])
                    
                    # Random modification
                    modified_or_not = random.choice(['modified', 'not modified'])
                    edited_reviews = None
                    if modified_or_not == 'modified':
                        edited_reviews = agent_reply + " (Updated response)"
                    
                    # Insert data
                    db.execute_update("""
                        INSERT INTO revs (
                            reviews, text, agent_reply, edited_reviews, modified_or_not, 
                            status, secondary_class, rating, is_deleted, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        review_text,
                        f"Customer feedback about {secondary_class.lower()}",
                        agent_reply,
                        edited_reviews,
                        modified_or_not,
                        status,
                        secondary_class,
                        rating,
                        0,  # is_deleted = 0 (not deleted)
                        created_time.strftime('%Y-%m-%d %H:%M:%S'),
                        created_time.strftime('%Y-%m-%d %H:%M:%S')
                    ))
                    
                    success_count += 1
                    
                except Exception as e:
                    print(f"Error inserting data: {e}")
        
        print(f"\n✅ Successfully generated {success_count} dummy reviews!")
        
        # Show breakdown by secondary class
        print("\n📊 Breakdown by Secondary Class:")
        for secondary_class in secondary_classes:
            count = db.execute_query(
                "SELECT COUNT(*) as count FROM revs WHERE secondary_class = ?", 
                (secondary_class,)
            )[0]['count']
            print(f"  {secondary_class}: {count} reviews")
        
        # Show total count
        total_count = db.execute_query("SELECT COUNT(*) as count FROM revs")[0]['count']
        print(f"\n📈 Total reviews in database: {total_count}")
        
        # Show rating distribution
        print("\n⭐ Rating Distribution:")
        ratings = db.execute_query("""
            SELECT 
                CASE 
                    WHEN rating >= 4.5 THEN '4.5-5.0'
                    WHEN rating >= 3.5 THEN '3.5-4.4'
                    WHEN rating >= 2.5 THEN '2.5-3.4'
                    WHEN rating >= 1.5 THEN '1.5-2.4'
                    ELSE '1.0-1.4'
                END as rating_range,
                COUNT(*) as count
            FROM revs 
            WHERE rating IS NOT NULL
            GROUP BY rating_range
            ORDER BY rating_range DESC
        """)
        
        for rating_row in ratings:
            print(f"  {rating_row['rating_range']}: {rating_row['count']} reviews")


if __name__ == "__main__":
    generate_dummy_data()
