# 📁 Excel Import Guide - Review Management System

## 🎯 Overview

You can now import review data from Excel files into your review management system using two methods:
1. **Streamlit Web Interface** - User-friendly GUI
2. **Command Line Tool** - For batch processing

## 📋 Excel File Format

### **Required Columns:**
- `reviews` - Customer review text
- `agent_reply` - Original agent response  
- `status` - Review status (active, pending, resolved, in_progress, archived)

### **Optional Columns:**
- `text` - Additional context/notes
- `edited_reviews` - Edited agent response (if modified)
- `modified_or_not` - Modification status (modified/not modified)

### **Sample Excel Structure:**
| reviews | text | agent_reply | edited_reviews | modified_or_not | status |
|---------|------|-------------|----------------|-----------------|--------|
| Great product! | Customer loves quality | Thank you! | | not modified | active |
| Could be better | Needs improvement | We appreciate feedback | We will improve based on your feedback | modified | pending |
| Excellent service | Great support | Glad to help | | not modified | resolved |

## 🌐 Method 1: Streamlit Web Interface

### **Steps:**
1. **Open your Streamlit app** at http://localhost:8501
2. **Click "📁 Import Data"** in the sidebar
3. **Download the sample template** (optional)
4. **Upload your Excel file**
5. **Review the data preview** and validation
6. **Choose import options**:
   - Add to existing data
   - Replace all data (⚠️ deletes existing reviews)
7. **Click "🚀 Import Data"**

### **Features:**
- ✅ **Data validation** with automatic fixing
- 📊 **Live preview** of your data
- 📥 **Sample template download**
- ⚙️ **Import options** (add vs replace)
- 🔍 **Column validation** and error checking
- 📈 **Import progress** and results

## 💻 Method 2: Command Line Tool

### **Interactive Mode:**
```bash
python import_excel.py
```

### **Command Line Mode:**
```bash
# Add to existing data
python import_excel.py your_file.xlsx

# Replace all existing data
python import_excel.py your_file.xlsx --replace
```

### **Create Sample File:**
```bash
python import_excel.py
# Choose option 2 to create sample_reviews.xlsx
```

## 📊 Data Validation

The system automatically validates:

### **Required Field Checks:**
- ✅ All required columns present
- ✅ No empty values in required fields
- ✅ Valid status values
- ✅ Valid modification status values

### **Automatic Fixes:**
- 🔧 Fill empty optional fields
- 🔧 Set default modification status
- 🔧 Fix invalid status values to 'pending'

### **Valid Status Values:**
- `active` - Currently active reviews
- `pending` - Awaiting action
- `resolved` - Completed/resolved
- `in_progress` - Being worked on
- `archived` - Archived reviews

### **Valid Modification Values:**
- `modified` - Review has been edited
- `not modified` - Original review unchanged

## 🎯 Use Cases

### **1. Initial Data Migration**
- Import existing review data from other systems
- Use "Replace all data" mode for clean import

### **2. Bulk Data Updates**
- Export current data, modify in Excel, re-import
- Use "Add to existing data" mode

### **3. Regular Data Imports**
- Import new reviews from external sources
- Automated batch processing via command line

### **4. Data Backup/Restore**
- Export data to Excel for backup
- Import to restore data

## 📁 File Management

### **Supported Formats:**
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)

### **Sample Files Created:**
- `sample_reviews.xlsx` - Template with sample data
- `review_template.xlsx` - Downloaded from Streamlit interface

## ⚠️ Important Notes

### **Data Safety:**
- **"Replace all data"** permanently deletes existing reviews
- **Always backup** your data before replacing
- **Test with small files** first

### **Performance:**
- Large files (1000+ rows) may take time to process
- Progress is shown during import
- Errors are reported for individual rows

### **Error Handling:**
- Invalid rows are skipped with error messages
- Import continues even if some rows fail
- Summary shows success/failure counts

## 🔧 Troubleshooting

### **Common Issues:**

#### **"Missing required columns"**
- Ensure your Excel has: `reviews`, `agent_reply`, `status`
- Check column names match exactly (case-sensitive)

#### **"Invalid status values"**
- Use only: active, pending, resolved, in_progress, archived
- Enable automatic fixing to convert invalid values

#### **"Error reading Excel file"**
- Ensure file is valid Excel format (.xlsx/.xls)
- Check file is not corrupted or password-protected
- Close Excel if file is open

#### **"Empty required fields"**
- Fill in all required columns
- Enable automatic fixing to handle empty optional fields

## 📈 After Import

### **Verify Import:**
1. Check **Dashboard** for updated counts
2. Review **Statistics** for data distribution
3. Use **Manage Reviews** to spot-check data

### **Data Quality:**
- Review imported data for accuracy
- Check status distributions make sense
- Verify modification flags are correct

## 🎉 Success Tips

1. **Start with sample template** - Download and modify the provided template
2. **Test with small files** - Import 5-10 rows first to verify format
3. **Use data validation** - Let the system fix common issues automatically
4. **Backup before replacing** - Export current data before major imports
5. **Check results** - Always verify import success in the dashboard

Your Excel import functionality is now ready to use! You can import data through either the web interface or command line tool based on your needs.
