import streamlit as st
import pandas as pd
from datetime import datetime
from database import Database
from revs_operations import *

# Configure the page
st.set_page_config(
    page_title="Review Management System",
    page_icon="📝",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .status-active { color: #28a745; font-weight: bold; }
    .status-pending { color: #ffc107; font-weight: bold; }
    .status-resolved { color: #6c757d; font-weight: bold; }
    .status-in_progress { color: #17a2b8; font-weight: bold; }
    .modified-yes { color: #dc3545; font-weight: bold; }
    .modified-no { color: #28a745; font-weight: bold; }
</style>
""", unsafe_allow_html=True)

def init_database():
    """Initialize database connection and create tables if needed."""
    try:
        with Database("app.db") as db:
            db.create_tables()
            # Create revs table if it doesn't exist
            try:
                db.execute_query("SELECT COUNT(*) FROM revs")
                # Table exists, check if edited_reviews column exists
                from database import add_edited_reviews_column
                add_edited_reviews_column(db)
            except:
                # Table doesn't exist, create it with new schema
                db.create_simple_table("revs", [
                    "id INTEGER PRIMARY KEY AUTOINCREMENT",
                    "reviews TEXT",
                    "text TEXT",
                    "agent_reply TEXT",
                    "edited_reviews TEXT",
                    "modified_or_not TEXT",
                    "status TEXT",
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
                ])
                # Add some sample data if table is empty
                sample_data = [
                    ("Great product!", "Customer loves the quality", "Thank you for your feedback!", None, "not modified", "active"),
                    ("Could be better", "Customer suggests improvements", "We appreciate your suggestions", None, "not modified", "pending"),
                    ("Excellent service", "Customer praises support team", "We're glad you had a great experience", None, "not modified", "active"),
                ]
                for data in sample_data:
                    db.execute_update(
                        "INSERT INTO revs (reviews, text, agent_reply, edited_reviews, modified_or_not, status) VALUES (?, ?, ?, ?, ?, ?)",
                        data
                    )
        return True
    except Exception as e:
        st.error(f"Database initialization failed: {e}")
        return False

def load_reviews():
    """Load all reviews from database."""
    try:
        with Database("app.db") as db:
            reviews = db.execute_query("SELECT * FROM revs ORDER BY created_at DESC")
            return reviews
    except Exception as e:
        st.error(f"Error loading reviews: {e}")
        return []

def update_agent_reply_db(review_id, new_reply):
    """Update agent reply in database by storing the new reply in edited_reviews column."""
    try:
        with Database("app.db") as db:
            affected_rows = db.execute_update(
                "UPDATE revs SET edited_reviews = ?, modified_or_not = 'modified', updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_reply, review_id)
            )
            return affected_rows > 0
    except Exception as e:
        st.error(f"Error updating reply: {e}")
        return False

def update_review_status_db(review_id, new_status):
    """Update review status in database."""
    try:
        with Database("app.db") as db:
            affected_rows = db.execute_update(
                "UPDATE revs SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_status, review_id)
            )
            return affected_rows > 0
    except Exception as e:
        st.error(f"Error updating status: {e}")
        return False



def get_statistics():
    """Get review statistics."""
    try:
        with Database("app.db") as db:
            stats = get_review_statistics(db)
            return stats
    except Exception as e:
        st.error(f"Error getting statistics: {e}")
        return {}

def main():
    """Main Streamlit application."""
    
    # Initialize database
    if not init_database():
        st.stop()
    
    # Header
    st.markdown('<h1 class="main-header">📝 Review Management System</h1>', unsafe_allow_html=True)
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["📊 Dashboard", "📝 Manage Reviews", "📈 Statistics", "📁 Import Data"]
    )

    if page == "📊 Dashboard":
        show_dashboard()
    elif page == "📝 Manage Reviews":
        show_manage_reviews()

    elif page == "📈 Statistics":
        show_statistics()
    elif page == "📁 Import Data":
        show_import_data()

def show_dashboard():
    """Show dashboard with overview."""
    st.header("Dashboard Overview")
    
    # Load reviews
    reviews = load_reviews()
    
    if not reviews:
        st.warning("No reviews found in the database.")
        return
    
    # Quick stats
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Reviews", len(reviews))
    
    with col2:
        active_count = len([r for r in reviews if r['status'] == 'active'])
        st.metric("Active Reviews", active_count)
    
    with col3:
        pending_count = len([r for r in reviews if r['status'] == 'pending'])
        st.metric("Pending Reviews", pending_count)
    
    with col4:
        modified_count = len([r for r in reviews if r['modified_or_not'] == 'modified'])
        st.metric("Modified Reviews", modified_count)
    
    # Recent reviews
    st.subheader("Recent Reviews")
    recent_reviews = reviews[:5]  # Show last 5
    
    for review in recent_reviews:
        with st.expander(f"Review #{review['id']}: {review['reviews'][:50]}..."):
            col1, col2 = st.columns([2, 1])

            with col1:
                st.write(f"**Review:** {review['reviews']}")
                st.write(f"**Original Agent Reply:** {review['agent_reply']}")

                # Show edited reply if it exists
                if review.get('edited_reviews'):
                    st.write(f"**Edited Agent Reply:** {review['edited_reviews']}")

            with col2:
                status_color = {
                    'active': '🟢', 'pending': '🟡',
                    'resolved': '⚫', 'in_progress': '🔵'
                }.get(review['status'], '⚪')

                st.write(f"**Status:** {status_color} {review['status']}")
                st.write(f"**Modified:** {'✅' if review['modified_or_not'] == 'modified' else '❌'}")
                st.write(f"**Created:** {review['created_at']}")

def show_manage_reviews():
    """Show review management interface."""
    st.header("Manage Reviews")
    
    # Load reviews
    reviews = load_reviews()
    
    if not reviews:
        st.warning("No reviews found in the database.")
        return
    
    # Filter options
    col1, col2 = st.columns(2)
    
    with col1:
        status_filter = st.selectbox(
            "Filter by Status:",
            ["All"] + list(set([r['status'] for r in reviews]))
        )
    
    with col2:
        modified_filter = st.selectbox(
            "Filter by Modified:",
            ["All", "modified", "not modified"]
        )
    
    # Apply filters
    filtered_reviews = reviews
    if status_filter != "All":
        filtered_reviews = [r for r in filtered_reviews if r['status'] == status_filter]
    if modified_filter != "All":
        filtered_reviews = [r for r in filtered_reviews if r['modified_or_not'] == modified_filter]
    
    st.write(f"Showing {len(filtered_reviews)} reviews")
    
    # Display reviews with edit capability
    for review in filtered_reviews:
        with st.expander(f"Review #{review['id']}: {review['reviews'][:60]}...", expanded=False):

            # Display review info
            col1, col2 = st.columns([3, 1])

            with col1:
                st.write(f"**Original Review:** {review['reviews']}")

                # Show original agent reply
                st.write("**Original Agent Reply:**")
                st.info(review['agent_reply'] or "No original reply")

                # Show edited reply if it exists
                if review.get('edited_reviews'):
                    st.write("**Edited Agent Reply:**")
                    st.success(review['edited_reviews'])

                # Editable agent reply section
                st.write("**Edit Agent Reply:**")
                # Use edited reply as default if it exists, otherwise use original
                current_reply = review.get('edited_reviews') or review['agent_reply'] or ""
                new_reply = st.text_area(
                    "New Agent Reply:",
                    value=current_reply,
                    key=f"reply_{review['id']}",
                    height=100,
                    placeholder="Enter your updated agent reply here..."
                )

                # Update button for reply
                if st.button(f"Update Reply", key=f"update_reply_{review['id']}"):
                    if new_reply != current_reply:
                        if update_agent_reply_db(review['id'], new_reply):
                            st.success("Agent reply updated successfully!")
                            st.rerun()
                        else:
                            st.error("Failed to update agent reply.")
                    else:
                        st.info("No changes made to the reply.")
            
            with col2:
                # Status update
                current_status = review['status']
                new_status = st.selectbox(
                    "Status:",
                    ["active", "pending", "resolved", "in_progress", "archived"],
                    index=["active", "pending", "resolved", "in_progress", "archived"].index(current_status) if current_status in ["active", "pending", "resolved", "in_progress", "archived"] else 0,
                    key=f"status_{review['id']}"
                )
                
                if st.button(f"Update Status", key=f"update_status_{review['id']}"):
                    if new_status != current_status:
                        if update_review_status_db(review['id'], new_status):
                            st.success("Status updated successfully!")
                            st.rerun()
                        else:
                            st.error("Failed to update status.")
                
                # Display info
                st.write(f"**Modified:** {'Yes' if review['modified_or_not'] == 'modified' else 'No'}")
                st.write(f"**Created:** {review['created_at']}")
                if review['updated_at'] != review['created_at']:
                    st.write(f"**Updated:** {review['updated_at']}")



def show_table_view():
    """Show raw database table view."""
    st.header("Database Table View")

    # Load reviews
    reviews = load_reviews()

    if not reviews:
        st.warning("No reviews found in the database.")
        return

    # Convert to DataFrame for better display
    df = pd.DataFrame(reviews)

    # Reorder columns for better readability
    column_order = ['id', 'reviews', 'agent_reply', 'edited_reviews', 'modified_or_not', 'status', 'created_at', 'updated_at']
    # Only include columns that exist in the dataframe
    available_columns = [col for col in column_order if col in df.columns]
    df = df[available_columns]

    # Display options
    st.subheader("Display Options")
    col1, col2, col3 = st.columns(3)

    with col1:
        show_all = st.checkbox("Show all columns", value=True)

    with col2:
        max_rows = st.number_input("Max rows to display", min_value=5, max_value=100, value=20)

    with col3:
        truncate_text = st.checkbox("Truncate long text", value=True)

    # Apply display options
    display_df = df.head(max_rows).copy()

    if truncate_text:
        # Truncate long text fields
        text_columns = ['reviews', 'agent_reply', 'edited_reviews']
        for col in text_columns:
            if col in display_df.columns:
                display_df[col] = display_df[col].astype(str).str[:50] + '...'

    if not show_all:
        # Show only essential columns
        essential_columns = ['id', 'reviews', 'agent_reply', 'edited_reviews', 'status', 'modified_or_not']
        available_essential = [col for col in essential_columns if col in display_df.columns]
        display_df = display_df[available_essential]

    # Display the table
    st.subheader(f"Reviews Table ({len(reviews)} total records)")
    st.dataframe(display_df, use_container_width=True)

    # Show column information
    st.subheader("Column Information")
    col_info = {
        'id': 'Unique identifier for each review',
        'reviews': 'Original customer review text',
        'agent_reply': 'Original agent response',
        'edited_reviews': 'Edited/updated agent response',
        'modified_or_not': 'Whether the review has been modified',
        'status': 'Current status of the review',
        'created_at': 'When the review was created',
        'updated_at': 'When the review was last updated'
    }

    for col, description in col_info.items():
        if col in df.columns:
            st.write(f"**{col}**: {description}")

    # Export option
    st.subheader("Export Data")
    if st.button("Download as CSV"):
        csv = df.to_csv(index=False)
        st.download_button(
            label="Download CSV file",
            data=csv,
            file_name="reviews_data.csv",
            mime="text/csv"
        )


def show_statistics():
    """Show comprehensive statistics and visualizations with KPIs."""
    st.header("📊 Analytics & KPIs Dashboard")

    # Load reviews for analysis
    reviews = load_reviews()

    if not reviews:
        st.warning("No reviews found for analysis.")
        return

    # Convert to DataFrame for analysis
    df = pd.DataFrame(reviews)
    df['created_at'] = pd.to_datetime(df['created_at'])
    df['updated_at'] = pd.to_datetime(df['updated_at'])

    # Calculate KPIs
    total_reviews = len(df)
    modified_reviews = len(df[df['modified_or_not'] == 'modified'])
    active_reviews = len(df[df['status'] == 'active'])
    pending_reviews = len(df[df['status'] == 'pending'])
    resolved_reviews = len(df[df['status'] == 'resolved'])

    # Calculate percentages
    modification_rate = (modified_reviews / total_reviews * 100) if total_reviews > 0 else 0
    resolution_rate = (resolved_reviews / total_reviews * 100) if total_reviews > 0 else 0
    active_rate = (active_reviews / total_reviews * 100) if total_reviews > 0 else 0

    # Reviews with edited replies
    reviews_with_edits = len(df[df['edited_reviews'].notna() & (df['edited_reviews'] != '')])
    edit_rate = (reviews_with_edits / total_reviews * 100) if total_reviews > 0 else 0

    # === KPI SECTION ===
    st.subheader("🎯 Key Performance Indicators")

    # Row 1 - Main KPIs
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="📝 Total Reviews",
            value=total_reviews,
            help="Total number of reviews in the system"
        )

    with col2:
        st.metric(
            label="✏️ Modification Rate",
            value=f"{modification_rate:.1f}%",
            delta=f"{modified_reviews} modified",
            help="Percentage of reviews that have been modified"
        )

    with col3:
        st.metric(
            label="✅ Resolution Rate",
            value=f"{resolution_rate:.1f}%",
            delta=f"{resolved_reviews} resolved",
            help="Percentage of reviews that have been resolved"
        )

    with col4:
        st.metric(
            label="🔄 Active Reviews",
            value=f"{active_rate:.1f}%",
            delta=f"{active_reviews} active",
            help="Percentage of reviews currently active"
        )

    # Row 2 - Secondary KPIs
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="📝 Edited Replies",
            value=f"{edit_rate:.1f}%",
            delta=f"{reviews_with_edits} edited",
            help="Percentage of reviews with edited agent replies"
        )

    with col2:
        pending_rate = (pending_reviews / total_reviews * 100) if total_reviews > 0 else 0
        st.metric(
            label="⏳ Pending Reviews",
            value=f"{pending_rate:.1f}%",
            delta=f"{pending_reviews} pending",
            help="Percentage of reviews pending action"
        )

    with col3:
        # Average response length
        avg_reply_length = df['agent_reply'].str.len().mean() if not df['agent_reply'].isna().all() else 0
        st.metric(
            label="📏 Avg Reply Length",
            value=f"{avg_reply_length:.0f} chars",
            help="Average length of agent replies"
        )

    with col4:
        # Reviews per day (if we have date range)
        date_range = (df['created_at'].max() - df['created_at'].min()).days
        reviews_per_day = total_reviews / max(date_range, 1)
        st.metric(
            label="📅 Reviews/Day",
            value=f"{reviews_per_day:.1f}",
            help="Average reviews per day"
        )

    st.divider()

    # === VISUALIZATIONS SECTION ===
    st.subheader("📈 Data Visualizations")

    # Row 1 - Status and Modification Charts
    col1, col2 = st.columns(2)

    with col1:
        st.write("**Status Distribution**")
        status_counts = df['status'].value_counts()

        # Use Streamlit's bar chart
        st.bar_chart(status_counts)

        # Show percentages
        st.write("**Status Breakdown:**")
        for status, count in status_counts.items():
            percentage = (count / total_reviews * 100)
            st.write(f"• {status.title()}: {count} ({percentage:.1f}%)")

    with col2:
        st.write("**Modification Status**")
        mod_counts = df['modified_or_not'].value_counts()
        st.bar_chart(mod_counts)

        # Show percentages
        st.write("**Modification Breakdown:**")
        for mod_status, count in mod_counts.items():
            percentage = (count / total_reviews * 100)
            st.write(f"• {mod_status.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")

    # Row 2 - Time-based Analysis
    st.subheader("📅 Time-based Analysis")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Reviews Over Time**")
        # Group by date
        df['date'] = df['created_at'].dt.date
        daily_reviews = df.groupby('date').size()
        st.line_chart(daily_reviews)

    with col2:
        st.write("**Review Activity by Hour**")
        df['hour'] = df['created_at'].dt.hour
        hourly_reviews = df.groupby('hour').size()
        st.bar_chart(hourly_reviews)

    # Row 3 - Advanced Analytics
    st.subheader("🔍 Advanced Analytics")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Reply Length Distribution**")
        # Calculate reply lengths
        df['reply_length'] = df['agent_reply'].str.len().fillna(0)

        # Create bins for reply lengths
        bins = [0, 50, 100, 200, 500, float('inf')]
        labels = ['0-50', '51-100', '101-200', '201-500', '500+']
        df['reply_length_bin'] = pd.cut(df['reply_length'], bins=bins, labels=labels, right=False)

        length_dist = df['reply_length_bin'].value_counts().sort_index()
        st.bar_chart(length_dist)

    with col2:
        st.write("**Modification vs Resolution Correlation**")

        # Cross-tabulation
        cross_tab = pd.crosstab(df['modified_or_not'], df['status'])
        st.write(cross_tab)

        # Show insights
        st.write("**Key Insights:**")
        modified_resolved = len(df[(df['modified_or_not'] == 'modified') & (df['status'] == 'resolved')])
        total_modified = len(df[df['modified_or_not'] == 'modified'])

        if total_modified > 0:
            resolution_rate_modified = (modified_resolved / total_modified * 100)
            st.write(f"• {resolution_rate_modified:.1f}% of modified reviews are resolved")

        unmodified_resolved = len(df[(df['modified_or_not'] == 'not modified') & (df['status'] == 'resolved')])
        total_unmodified = len(df[df['modified_or_not'] == 'not modified'])

        if total_unmodified > 0:
            resolution_rate_unmodified = (unmodified_resolved / total_unmodified * 100)
            st.write(f"• {resolution_rate_unmodified:.1f}% of unmodified reviews are resolved")

    # Summary Section
    st.divider()
    st.subheader("📋 Summary Report")

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Performance Summary:**")
        st.write(f"• Total reviews processed: **{total_reviews}**")
        st.write(f"• Modification rate: **{modification_rate:.1f}%**")
        st.write(f"• Resolution rate: **{resolution_rate:.1f}%**")
        st.write(f"• Average reply length: **{avg_reply_length:.0f} characters**")

    with col2:
        st.write("**Recommendations:**")

        if modification_rate > 50:
            st.write("🔴 High modification rate - consider improving initial responses")
        elif modification_rate > 25:
            st.write("🟡 Moderate modification rate - room for improvement")
        else:
            st.write("🟢 Low modification rate - good initial response quality")

        if resolution_rate > 75:
            st.write("🟢 High resolution rate - excellent performance")
        elif resolution_rate > 50:
            st.write("🟡 Moderate resolution rate - consider follow-up strategies")
        else:
            st.write("🔴 Low resolution rate - needs attention")

        if pending_reviews > total_reviews * 0.3:
            st.write("🔴 High pending reviews - prioritize processing")
        else:
            st.write("🟢 Manageable pending reviews")

def show_import_data():
    """Show Excel import functionality."""
    st.header("📁 Import Data from Excel")

    st.markdown("""
    Upload an Excel file to import review data into your database.
    The Excel file should have the following columns:
    """)

    # Show expected format
    st.subheader("📋 Expected Excel Format")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("""
        **Required Columns:**
        - `reviews` - Customer review text
        - `agent_reply` - Original agent response
        - `status` - Review status (active, pending, resolved, in_progress)

        **Optional Columns:**
        - `text` - Additional context/notes
        - `edited_reviews` - Edited agent response
        - `modified_or_not` - Modification status (modified/not modified)
        """)

    with col2:
        # Create sample Excel file for download
        sample_data = {
            'reviews': [
                'Great product!',
                'Could be better',
                'Excellent service'
            ],
            'text': [
                'Customer loves the quality',
                'Customer suggests improvements',
                'Customer praises support team'
            ],
            'agent_reply': [
                'Thank you for your feedback!',
                'We appreciate your suggestions',
                'We\'re glad you had a great experience'
            ],
            'edited_reviews': [
                '',
                'We appreciate your suggestions and will improve',
                ''
            ],
            'modified_or_not': [
                'not modified',
                'modified',
                'not modified'
            ],
            'status': [
                'active',
                'pending',
                'resolved'
            ]
        }

        sample_df = pd.DataFrame(sample_data)

        # Convert to Excel bytes for download
        from io import BytesIO
        excel_buffer = BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            sample_df.to_excel(writer, index=False, sheet_name='Reviews')
        excel_buffer.seek(0)

        st.download_button(
            label="📥 Download Sample Excel Template",
            data=excel_buffer.getvalue(),
            file_name="review_template.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

    st.divider()

    # File upload section
    st.subheader("📤 Upload Excel File")

    uploaded_file = st.file_uploader(
        "Choose an Excel file",
        type=['xlsx', 'xls'],
        help="Upload an Excel file with review data"
    )

    if uploaded_file is not None:
        try:
            # Read the Excel file
            df = pd.read_excel(uploaded_file)

            st.success(f"✅ File uploaded successfully! Found {len(df)} rows.")

            # Show preview
            st.subheader("📊 Data Preview")
            st.dataframe(df.head(10), use_container_width=True)

            # Validate columns
            st.subheader("🔍 Column Validation")

            required_columns = ['reviews', 'agent_reply', 'status']
            optional_columns = ['text', 'edited_reviews', 'modified_or_not']

            missing_required = [col for col in required_columns if col not in df.columns]
            available_optional = [col for col in optional_columns if col in df.columns]

            if missing_required:
                st.error(f"❌ Missing required columns: {missing_required}")
                st.stop()
            else:
                st.success("✅ All required columns found!")

            if available_optional:
                st.info(f"ℹ️ Optional columns found: {available_optional}")

            # Data validation
            st.subheader("✅ Data Validation")

            # Check for empty required fields
            validation_issues = []

            for col in required_columns:
                empty_count = df[col].isna().sum()
                if empty_count > 0:
                    validation_issues.append(f"Column '{col}' has {empty_count} empty values")

            # Check status values
            valid_statuses = ['active', 'pending', 'resolved', 'in_progress', 'archived']
            invalid_statuses = df[~df['status'].isin(valid_statuses)]['status'].unique()
            if len(invalid_statuses) > 0:
                validation_issues.append(f"Invalid status values found: {list(invalid_statuses)}")

            # Check modified_or_not values if present
            if 'modified_or_not' in df.columns:
                valid_modified = ['modified', 'not modified']
                invalid_modified = df[~df['modified_or_not'].isin(valid_modified)]['modified_or_not'].unique()
                if len(invalid_modified) > 0:
                    validation_issues.append(f"Invalid modification values found: {list(invalid_modified)}")

            if validation_issues:
                st.warning("⚠️ Data validation issues found:")
                for issue in validation_issues:
                    st.write(f"• {issue}")

                fix_data = st.checkbox("🔧 Attempt to fix data automatically")

                if fix_data:
                    # Fill missing values
                    df['text'] = df['text'].fillna('')
                    df['edited_reviews'] = df['edited_reviews'].fillna('')
                    if 'modified_or_not' not in df.columns:
                        df['modified_or_not'] = 'not modified'
                    df['modified_or_not'] = df['modified_or_not'].fillna('not modified')

                    # Fix invalid statuses
                    df.loc[~df['status'].isin(valid_statuses), 'status'] = 'pending'

                    st.success("✅ Data automatically fixed!")
            else:
                st.success("✅ Data validation passed!")

            # Import options
            st.subheader("⚙️ Import Options")

            col1, col2 = st.columns(2)

            with col1:
                import_mode = st.radio(
                    "Import Mode:",
                    ["Add to existing data", "Replace all data"],
                    help="Choose whether to add to existing reviews or replace all data"
                )

            with col2:
                if import_mode == "Replace all data":
                    st.warning("⚠️ This will delete all existing reviews!")
                    confirm_replace = st.checkbox("I understand this will delete all existing data")
                else:
                    confirm_replace = True

            # Import button
            if st.button("🚀 Import Data", type="primary", disabled=not confirm_replace):

                with st.spinner("Importing data..."):
                    try:
                        with Database("app.db") as db:

                            if import_mode == "Replace all data":
                                # Delete existing data
                                db.execute_update("DELETE FROM revs")
                                st.info("🗑️ Existing data cleared")

                            # Prepare data for insertion
                            success_count = 0
                            error_count = 0

                            for index, row in df.iterrows():
                                try:
                                    # Prepare values
                                    reviews = row['reviews']
                                    text = row.get('text', '')
                                    agent_reply = row['agent_reply']
                                    edited_reviews = row.get('edited_reviews', None)
                                    if edited_reviews == '':
                                        edited_reviews = None
                                    modified_or_not = row.get('modified_or_not', 'not modified')
                                    status = row['status']

                                    # Insert into database
                                    db.execute_update("""
                                        INSERT INTO revs (reviews, text, agent_reply, edited_reviews, modified_or_not, status)
                                        VALUES (?, ?, ?, ?, ?, ?)
                                    """, (reviews, text, agent_reply, edited_reviews, modified_or_not, status))

                                    success_count += 1

                                except Exception as e:
                                    error_count += 1
                                    st.error(f"Error importing row {index + 1}: {e}")

                        # Show results
                        if success_count > 0:
                            st.success(f"✅ Successfully imported {success_count} reviews!")

                        if error_count > 0:
                            st.error(f"❌ Failed to import {error_count} reviews")

                        # Show updated statistics
                        with Database("app.db") as db:
                            total_reviews = db.execute_query("SELECT COUNT(*) as count FROM revs")[0]['count']
                            st.info(f"📊 Total reviews in database: {total_reviews}")

                        st.balloons()

                    except Exception as e:
                        st.error(f"❌ Import failed: {e}")

        except Exception as e:
            st.error(f"❌ Error reading Excel file: {e}")
            st.info("Please make sure the file is a valid Excel file (.xlsx or .xls)")


if __name__ == "__main__":
    main()
