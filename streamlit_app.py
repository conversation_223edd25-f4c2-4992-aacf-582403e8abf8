import streamlit as st
import pandas as pd
from datetime import datetime
from database import Database
from revs_operations import *

# Configure the page
st.set_page_config(
    page_title="Review Management System",
    page_icon="📝",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .status-active { color: #28a745; font-weight: bold; }
    .status-pending { color: #ffc107; font-weight: bold; }
    .status-resolved { color: #6c757d; font-weight: bold; }
    .status-in_progress { color: #17a2b8; font-weight: bold; }
    .modified-yes { color: #dc3545; font-weight: bold; }
    .modified-no { color: #28a745; font-weight: bold; }
</style>
""", unsafe_allow_html=True)

def init_database():
    """Initialize database connection and create tables if needed."""
    try:
        with Database("app.db") as db:
            db.create_tables()
            # Create revs table if it doesn't exist
            try:
                db.execute_query("SELECT COUNT(*) FROM revs")
                # Table exists, check if edited_reviews column exists
                from database import add_edited_reviews_column
                add_edited_reviews_column(db)
            except:
                # Table doesn't exist, create it with new schema
                db.create_simple_table("revs", [
                    "id INTEGER PRIMARY KEY AUTOINCREMENT",
                    "reviews TEXT",
                    "text TEXT",
                    "agent_reply TEXT",
                    "edited_reviews TEXT",
                    "modified_or_not TEXT",
                    "status TEXT",
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
                ])
                # Add some sample data if table is empty
                sample_data = [
                    ("Great product!", "Customer loves the quality", "Thank you for your feedback!", None, "not modified", "active"),
                    ("Could be better", "Customer suggests improvements", "We appreciate your suggestions", None, "not modified", "pending"),
                    ("Excellent service", "Customer praises support team", "We're glad you had a great experience", None, "not modified", "active"),
                ]
                for data in sample_data:
                    db.execute_update(
                        "INSERT INTO revs (reviews, text, agent_reply, edited_reviews, modified_or_not, status) VALUES (?, ?, ?, ?, ?, ?)",
                        data
                    )
        return True
    except Exception as e:
        st.error(f"Database initialization failed: {e}")
        return False

def load_reviews():
    """Load all reviews from database."""
    try:
        with Database("app.db") as db:
            reviews = db.execute_query("SELECT * FROM revs ORDER BY created_at DESC")
            return reviews
    except Exception as e:
        st.error(f"Error loading reviews: {e}")
        return []

def update_agent_reply_db(review_id, new_reply):
    """Update agent reply in database by storing the new reply in edited_reviews column."""
    try:
        with Database("app.db") as db:
            affected_rows = db.execute_update(
                "UPDATE revs SET edited_reviews = ?, modified_or_not = 'modified', updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_reply, review_id)
            )
            return affected_rows > 0
    except Exception as e:
        st.error(f"Error updating reply: {e}")
        return False

def update_review_status_db(review_id, new_status):
    """Update review status in database."""
    try:
        with Database("app.db") as db:
            affected_rows = db.execute_update(
                "UPDATE revs SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (new_status, review_id)
            )
            return affected_rows > 0
    except Exception as e:
        st.error(f"Error updating status: {e}")
        return False



def get_statistics():
    """Get review statistics."""
    try:
        with Database("app.db") as db:
            stats = get_review_statistics(db)
            return stats
    except Exception as e:
        st.error(f"Error getting statistics: {e}")
        return {}

def main():
    """Main Streamlit application."""
    
    # Initialize database
    if not init_database():
        st.stop()
    
    # Header
    st.markdown('<h1 class="main-header">📝 Review Management System</h1>', unsafe_allow_html=True)
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["📊 Dashboard", "📝 Manage Reviews", "📈 Statistics"]
    )

    if page == "📊 Dashboard":
        show_dashboard()
    elif page == "📝 Manage Reviews":
        show_manage_reviews()
    elif page == "📈 Statistics":
        show_statistics()

def show_dashboard():
    """Show dashboard with overview."""
    st.header("Dashboard Overview")
    
    # Load reviews
    reviews = load_reviews()
    
    if not reviews:
        st.warning("No reviews found in the database.")
        return
    
    # Quick stats
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Reviews", len(reviews))
    
    with col2:
        active_count = len([r for r in reviews if r['status'] == 'active'])
        st.metric("Active Reviews", active_count)
    
    with col3:
        pending_count = len([r for r in reviews if r['status'] == 'pending'])
        st.metric("Pending Reviews", pending_count)
    
    with col4:
        modified_count = len([r for r in reviews if r['modified_or_not'] == 'modified'])
        st.metric("Modified Reviews", modified_count)
    
    # Recent reviews
    st.subheader("Recent Reviews")
    recent_reviews = reviews[:5]  # Show last 5
    
    for review in recent_reviews:
        with st.expander(f"Review #{review['id']}: {review['reviews'][:50]}..."):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.write(f"**Review:** {review['reviews']}")
                st.write(f"**Text:** {review['text']}")
                st.write(f"**Agent Reply:** {review['agent_reply']}")
            
            with col2:
                status_color = {
                    'active': '🟢', 'pending': '🟡', 
                    'resolved': '⚫', 'in_progress': '🔵'
                }.get(review['status'], '⚪')
                
                st.write(f"**Status:** {status_color} {review['status']}")
                st.write(f"**Modified:** {'✅' if review['modified_or_not'] == 'modified' else '❌'}")
                st.write(f"**Created:** {review['created_at']}")

def show_manage_reviews():
    """Show review management interface."""
    st.header("Manage Reviews")
    
    # Load reviews
    reviews = load_reviews()
    
    if not reviews:
        st.warning("No reviews found in the database.")
        return
    
    # Filter options
    col1, col2 = st.columns(2)
    
    with col1:
        status_filter = st.selectbox(
            "Filter by Status:",
            ["All"] + list(set([r['status'] for r in reviews]))
        )
    
    with col2:
        modified_filter = st.selectbox(
            "Filter by Modified:",
            ["All", "modified", "not modified"]
        )
    
    # Apply filters
    filtered_reviews = reviews
    if status_filter != "All":
        filtered_reviews = [r for r in filtered_reviews if r['status'] == status_filter]
    if modified_filter != "All":
        filtered_reviews = [r for r in filtered_reviews if r['modified_or_not'] == modified_filter]
    
    st.write(f"Showing {len(filtered_reviews)} reviews")
    
    # Display reviews with edit capability
    for review in filtered_reviews:
        with st.expander(f"Review #{review['id']}: {review['reviews'][:60]}...", expanded=False):

            # Display review info
            col1, col2 = st.columns([3, 1])

            with col1:
                st.write(f"**Original Review:** {review['reviews']}")
                st.write(f"**Additional Text:** {review['text']}")

                # Show original agent reply
                st.write("**Original Agent Reply:**")
                st.info(review['agent_reply'] or "No original reply")

                # Show edited reply if it exists
                if review.get('edited_reviews'):
                    st.write("**Edited Agent Reply:**")
                    st.success(review['edited_reviews'])

                # Editable agent reply section
                st.write("**Edit Agent Reply:**")
                # Use edited reply as default if it exists, otherwise use original
                current_reply = review.get('edited_reviews') or review['agent_reply'] or ""
                new_reply = st.text_area(
                    "New Agent Reply:",
                    value=current_reply,
                    key=f"reply_{review['id']}",
                    height=100,
                    placeholder="Enter your updated agent reply here..."
                )

                # Update button for reply
                if st.button(f"Update Reply", key=f"update_reply_{review['id']}"):
                    if new_reply != current_reply:
                        if update_agent_reply_db(review['id'], new_reply):
                            st.success("Agent reply updated successfully!")
                            st.rerun()
                        else:
                            st.error("Failed to update agent reply.")
                    else:
                        st.info("No changes made to the reply.")
            
            with col2:
                # Status update
                current_status = review['status']
                new_status = st.selectbox(
                    "Status:",
                    ["active", "pending", "resolved", "in_progress", "archived"],
                    index=["active", "pending", "resolved", "in_progress", "archived"].index(current_status) if current_status in ["active", "pending", "resolved", "in_progress", "archived"] else 0,
                    key=f"status_{review['id']}"
                )
                
                if st.button(f"Update Status", key=f"update_status_{review['id']}"):
                    if new_status != current_status:
                        if update_review_status_db(review['id'], new_status):
                            st.success("Status updated successfully!")
                            st.rerun()
                        else:
                            st.error("Failed to update status.")
                
                # Display info
                st.write(f"**Modified:** {'Yes' if review['modified_or_not'] == 'modified' else 'No'}")
                st.write(f"**Created:** {review['created_at']}")
                if review['updated_at'] != review['created_at']:
                    st.write(f"**Updated:** {review['updated_at']}")



def show_statistics():
    """Show statistics page."""
    st.header("Review Statistics")
    
    stats = get_statistics()
    
    if not stats:
        st.warning("No statistics available.")
        return
    
    # Display statistics
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Overall Statistics")
        st.metric("Total Reviews", stats.get('total_reviews', 0))
        st.metric("Modified Reviews", stats.get('modified', 0))
        st.metric("Unmodified Reviews", stats.get('unmodified', 0))
    
    with col2:
        st.subheader("Status Breakdown")
        for key, value in stats.items():
            if key.startswith('status_'):
                status_name = key.replace('status_', '').title()
                st.metric(f"{status_name} Reviews", value)
    
    # Load reviews for charts
    reviews = load_reviews()
    if reviews:
        # Create DataFrame for visualization
        df = pd.DataFrame(reviews)
        
        # Status distribution
        st.subheader("Status Distribution")
        status_counts = df['status'].value_counts()
        st.bar_chart(status_counts)
        
        # Modified vs Unmodified
        st.subheader("Modification Status")
        mod_counts = df['modified_or_not'].value_counts()
        st.bar_chart(mod_counts)

if __name__ == "__main__":
    main()
