#!/usr/bin/env python3
"""
Interactive Review Manager
Simple command-line interface to manage your reviews database.
"""

from database import Database
from revs_operations import *


def display_menu():
    """Display the main menu options."""
    print("\n" + "="*50)
    print("         REVIEW MANAGER")
    print("="*50)
    print("1.  Add new review")
    print("2.  View all reviews")
    print("3.  View reviews by status")
    print("4.  View modified reviews")
    print("5.  View unmodified reviews")
    print("6.  Search reviews")
    print("7.  Update review status")
    print("8.  Update agent reply")
    print("9.  Mark review as modified")
    print("10. View statistics")
    print("11. View recent reviews")
    print("12. Delete review")
    print("0.  Exit")
    print("="*50)


def get_user_input(prompt: str, required: bool = True) -> str:
    """Get user input with validation."""
    while True:
        value = input(prompt).strip()
        if value or not required:
            return value
        print("This field is required. Please enter a value.")


def add_review_interactive(db: Database):
    """Interactive function to add a new review."""
    print("\n--- Add New Review ---")
    reviews = get_user_input("Enter review content: ")
    text = get_user_input("Enter additional text: ", required=False)
    agent_reply = get_user_input("Enter agent reply: ", required=False)
    
    print("\nModification status options: 'modified' or 'not modified'")
    modified_or_not = get_user_input("Is this modified? (modified/not modified): ")
    while modified_or_not not in ['modified', 'not modified']:
        print("Please enter either 'modified' or 'not modified'")
        modified_or_not = get_user_input("Is this modified? (modified/not modified): ")
    
    print("\nStatus options: active, pending, resolved, in_progress, archived")
    status = get_user_input("Enter status: ")
    
    add_new_review(db, reviews, text, agent_reply, modified_or_not, status)
    print("Review added successfully!")


def view_by_status_interactive(db: Database):
    """Interactive function to view reviews by status."""
    print("\n--- View Reviews by Status ---")
    
    # Show available statuses
    statuses = db.execute_query("SELECT DISTINCT status FROM revs ORDER BY status")
    if not statuses:
        print("No reviews found in database.")
        return
    
    print("Available statuses:")
    for i, status_row in enumerate(statuses, 1):
        print(f"{i}. {status_row['status']}")
    
    try:
        choice = int(get_user_input("Enter status number: ")) - 1
        if 0 <= choice < len(statuses):
            selected_status = statuses[choice]['status']
            reviews = get_reviews_by_status(db, selected_status)
            print_reviews(reviews, f"Reviews with status: {selected_status}")
        else:
            print("Invalid choice.")
    except ValueError:
        print("Please enter a valid number.")


def search_interactive(db: Database):
    """Interactive function to search reviews."""
    print("\n--- Search Reviews ---")
    search_term = get_user_input("Enter search term: ")
    results = search_reviews(db, search_term)
    print_reviews(results, f"Search results for: '{search_term}'")


def update_status_interactive(db: Database):
    """Interactive function to update review status."""
    print("\n--- Update Review Status ---")
    
    # Show recent reviews for reference
    recent = get_recent_reviews(db, 10)
    if not recent:
        print("No reviews found.")
        return
    
    print("Recent reviews:")
    for review in recent:
        print(f"ID: {review['id']} - {review['reviews'][:50]}... (Status: {review['status']})")
    
    try:
        review_id = int(get_user_input("Enter review ID to update: "))
        new_status = get_user_input("Enter new status: ")
        update_review_status(db, review_id, new_status)
    except ValueError:
        print("Please enter a valid review ID (number).")


def update_reply_interactive(db: Database):
    """Interactive function to update agent reply."""
    print("\n--- Update Agent Reply ---")
    
    # Show recent reviews for reference
    recent = get_recent_reviews(db, 10)
    if not recent:
        print("No reviews found.")
        return
    
    print("Recent reviews:")
    for review in recent:
        print(f"ID: {review['id']} - {review['reviews'][:50]}...")
        print(f"   Current reply: {review['agent_reply'][:50]}...")
    
    try:
        review_id = int(get_user_input("Enter review ID to update: "))
        new_reply = get_user_input("Enter new agent reply: ")
        update_agent_reply(db, review_id, new_reply)
    except ValueError:
        print("Please enter a valid review ID (number).")


def mark_modified_interactive(db: Database):
    """Interactive function to mark review as modified."""
    print("\n--- Mark Review as Modified ---")
    
    # Show unmodified reviews
    unmodified = get_unmodified_reviews(db)
    if not unmodified:
        print("No unmodified reviews found.")
        return
    
    print("Unmodified reviews:")
    for review in unmodified:
        print(f"ID: {review['id']} - {review['reviews'][:50]}...")
    
    try:
        review_id = int(get_user_input("Enter review ID to mark as modified: "))
        mark_as_modified(db, review_id)
    except ValueError:
        print("Please enter a valid review ID (number).")


def delete_review_interactive(db: Database):
    """Interactive function to delete a review."""
    print("\n--- Delete Review ---")
    print("⚠️  WARNING: This action cannot be undone!")
    
    # Show all reviews for reference
    all_reviews = get_all_reviews(db)
    if not all_reviews:
        print("No reviews found.")
        return
    
    print("All reviews:")
    for review in all_reviews:
        print(f"ID: {review['id']} - {review['reviews'][:50]}... (Status: {review['status']})")
    
    try:
        review_id = int(get_user_input("Enter review ID to DELETE: "))
        confirm = get_user_input(f"Are you sure you want to delete review ID {review_id}? (yes/no): ")
        if confirm.lower() == 'yes':
            delete_review(db, review_id)
        else:
            print("Delete operation cancelled.")
    except ValueError:
        print("Please enter a valid review ID (number).")


def main():
    """Main interactive loop."""
    print("Welcome to Review Manager!")
    
    with Database("app.db") as db:
        while True:
            display_menu()
            
            try:
                choice = int(input("Enter your choice (0-12): "))
                
                if choice == 0:
                    print("Goodbye!")
                    break
                elif choice == 1:
                    add_review_interactive(db)
                elif choice == 2:
                    reviews = get_all_reviews(db)
                    print_reviews(reviews, "All Reviews")
                elif choice == 3:
                    view_by_status_interactive(db)
                elif choice == 4:
                    reviews = get_modified_reviews(db)
                    print_reviews(reviews, "Modified Reviews")
                elif choice == 5:
                    reviews = get_unmodified_reviews(db)
                    print_reviews(reviews, "Unmodified Reviews")
                elif choice == 6:
                    search_interactive(db)
                elif choice == 7:
                    update_status_interactive(db)
                elif choice == 8:
                    update_reply_interactive(db)
                elif choice == 9:
                    mark_modified_interactive(db)
                elif choice == 10:
                    stats = get_review_statistics(db)
                    print("\n--- Review Statistics ---")
                    for key, value in stats.items():
                        print(f"{key.replace('_', ' ').title()}: {value}")
                elif choice == 11:
                    limit = int(get_user_input("How many recent reviews to show? (default 5): ") or "5")
                    reviews = get_recent_reviews(db, limit)
                    print_reviews(reviews, f"Recent {limit} Reviews")
                elif choice == 12:
                    delete_review_interactive(db)
                else:
                    print("Invalid choice. Please enter a number between 0-12.")
                    
                input("\nPress Enter to continue...")
                
            except ValueError:
                print("Please enter a valid number.")
                input("Press Enter to continue...")
            except KeyboardInterrupt:
                print("\n\nGoodbye!")
                break
            except Exception as e:
                print(f"An error occurred: {e}")
                input("Press Enter to continue...")


if __name__ == "__main__":
    main()
