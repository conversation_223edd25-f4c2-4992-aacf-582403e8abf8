import sqlite3
import os
from typing import Optional, List, Dict, Any


class Database:
    """A simple SQLite database wrapper class."""
    
    def __init__(self, db_path: str = "app.db"):
        """
        Initialize the database connection.
        
        Args:
            db_path (str): Path to the SQLite database file
        """
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
        self.cursor: Optional[sqlite3.Cursor] = None
    
    def connect(self) -> None:
        """Establish connection to the database."""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Enable dict-like access to rows
            self.cursor = self.connection.cursor()
            print(f"Connected to database: {self.db_path}")
        except sqlite3.Error as e:
            print(f"Error connecting to database: {e}")
            raise
    
    def disconnect(self) -> None:
        """Close the database connection."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("Database connection closed")
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        Execute a SELECT query and return results.
        
        Args:
            query (str): SQL query to execute
            params (tuple): Parameters for the query
            
        Returns:
            List[Dict[str, Any]]: Query results as list of dictionaries
        """
        if not self.cursor:
            raise RuntimeError("Database not connected. Call connect() first.")
        
        try:
            self.cursor.execute(query, params)
            rows = self.cursor.fetchall()
            return [dict(row) for row in rows]
        except sqlite3.Error as e:
            print(f"Error executing query: {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        Execute an INSERT, UPDATE, or DELETE query.
        
        Args:
            query (str): SQL query to execute
            params (tuple): Parameters for the query
            
        Returns:
            int: Number of affected rows
        """
        if not self.cursor or not self.connection:
            raise RuntimeError("Database not connected. Call connect() first.")
        
        try:
            self.cursor.execute(query, params)
            self.connection.commit()
            return self.cursor.rowcount
        except sqlite3.Error as e:
            self.connection.rollback()
            print(f"Error executing update: {e}")
            raise
    
    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """
        Execute a query multiple times with different parameters.
        
        Args:
            query (str): SQL query to execute
            params_list (List[tuple]): List of parameter tuples
            
        Returns:
            int: Number of affected rows
        """
        if not self.cursor or not self.connection:
            raise RuntimeError("Database not connected. Call connect() first.")
        
        try:
            self.cursor.executemany(query, params_list)
            self.connection.commit()
            return self.cursor.rowcount
        except sqlite3.Error as e:
            self.connection.rollback()
            print(f"Error executing batch update: {e}")
            raise
    
    def create_tables(self) -> None:
        """Create the initial database tables."""
        tables = [
            """
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
            """
        ]
        
        for table_sql in tables:
            self.execute_update(table_sql)
        
        print("Database tables created successfully")
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


# Example usage functions
def insert_sample_data(db: Database) -> None:
    """Insert some sample data into the database."""
    # Insert users
    users = [
        ("john_doe", "<EMAIL>"),
        ("jane_smith", "<EMAIL>"),
        ("bob_wilson", "<EMAIL>")
    ]
    
    user_query = "INSERT INTO users (username, email) VALUES (?, ?)"
    db.execute_many(user_query, users)
    
    # Insert posts
    posts = [
        (1, "First Post", "This is my first post!"),
        (1, "Another Post", "Here's another post by John."),
        (2, "Jane's Post", "Hello from Jane!"),
        (3, "Bob's Thoughts", "Some thoughts from Bob.")
    ]
    
    post_query = "INSERT INTO posts (user_id, title, content) VALUES (?, ?, ?)"
    db.execute_many(post_query, posts)
    
    print("Sample data inserted successfully")


def query_sample_data(db: Database) -> None:
    """Query and display sample data."""
    # Get all users
    users = db.execute_query("SELECT * FROM users")
    print("\n--- Users ---")
    for user in users:
        print(f"ID: {user['id']}, Username: {user['username']}, Email: {user['email']}")
    
    # Get posts with user information
    posts_query = """
    SELECT p.id, p.title, p.content, u.username, p.created_at
    FROM posts p
    JOIN users u ON p.user_id = u.id
    ORDER BY p.created_at DESC
    """
    posts = db.execute_query(posts_query)
    print("\n--- Posts ---")
    for post in posts:
        print(f"'{post['title']}' by {post['username']}")
        print(f"Content: {post['content']}")
        print(f"Created: {post['created_at']}\n")


if __name__ == "__main__":
    # Example usage
    with Database("app.db") as db:
        # Create tables
        db.create_tables()
        
        # Check if we need to insert sample data
        users = db.execute_query("SELECT COUNT(*) as count FROM users")
        if users[0]['count'] == 0:
            insert_sample_data(db)
        
        # Query and display data
        query_sample_data(db)
