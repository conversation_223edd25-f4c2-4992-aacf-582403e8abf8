import sqlite3
import os
from typing import Optional, List, Dict, Any


class Database:
    """A simple SQLite database wrapper class."""
    
    def __init__(self, db_path: str = "app.db"):
        """
        Initialize the database connection.
        
        Args:
            db_path (str): Path to the SQLite database file
        """
        self.db_path = db_path
        self.connection: Optional[sqlite3.Connection] = None
        self.cursor: Optional[sqlite3.Cursor] = None
    
    def connect(self) -> None:
        """Establish connection to the database."""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Enable dict-like access to rows
            self.cursor = self.connection.cursor()
            print(f"Connected to database: {self.db_path}")
        except sqlite3.Error as e:
            print(f"Error connecting to database: {e}")
            raise
    
    def disconnect(self) -> None:
        """Close the database connection."""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("Database connection closed")
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict[str, Any]]:
        """
        Execute a SELECT query and return results.
        
        Args:
            query (str): SQL query to execute
            params (tuple): Parameters for the query
            
        Returns:
            List[Dict[str, Any]]: Query results as list of dictionaries
        """
        if not self.cursor:
            raise RuntimeError("Database not connected. Call connect() first.")
        
        try:
            self.cursor.execute(query, params)
            rows = self.cursor.fetchall()
            return [dict(row) for row in rows]
        except sqlite3.Error as e:
            print(f"Error executing query: {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        Execute an INSERT, UPDATE, or DELETE query.
        
        Args:
            query (str): SQL query to execute
            params (tuple): Parameters for the query
            
        Returns:
            int: Number of affected rows
        """
        if not self.cursor or not self.connection:
            raise RuntimeError("Database not connected. Call connect() first.")
        
        try:
            self.cursor.execute(query, params)
            self.connection.commit()
            return self.cursor.rowcount
        except sqlite3.Error as e:
            self.connection.rollback()
            print(f"Error executing update: {e}")
            raise
    
    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """
        Execute a query multiple times with different parameters.
        
        Args:
            query (str): SQL query to execute
            params_list (List[tuple]): List of parameter tuples
            
        Returns:
            int: Number of affected rows
        """
        if not self.cursor or not self.connection:
            raise RuntimeError("Database not connected. Call connect() first.")
        
        try:
            self.cursor.executemany(query, params_list)
            self.connection.commit()
            return self.cursor.rowcount
        except sqlite3.Error as e:
            self.connection.rollback()
            print(f"Error executing batch update: {e}")
            raise
    
    def create_tables(self) -> None:
        """Create the initial database tables."""
        tables = [
            """
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            """
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                content TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
            """
        ]

        for table_sql in tables:
            self.execute_update(table_sql)

        print("Database tables created successfully")

    def create_custom_table(self, table_name: str, columns: Dict[str, str],
                           foreign_keys: Optional[List[str]] = None) -> None:
        """
        Create a custom table with specified columns.

        Args:
            table_name (str): Name of the table to create
            columns (Dict[str, str]): Dictionary of column_name: column_definition
            foreign_keys (Optional[List[str]]): List of foreign key constraints

        Example:
            db.create_custom_table(
                "products",
                {
                    "id": "INTEGER PRIMARY KEY AUTOINCREMENT",
                    "name": "TEXT NOT NULL",
                    "price": "REAL NOT NULL",
                    "description": "TEXT",
                    "category_id": "INTEGER",
                    "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
                },
                ["FOREIGN KEY (category_id) REFERENCES categories (id)"]
            )
        """
        if not self.cursor or not self.connection:
            raise RuntimeError("Database not connected. Call connect() first.")

        # Build column definitions
        column_defs = []
        for col_name, col_def in columns.items():
            column_defs.append(f"{col_name} {col_def}")

        # Add foreign key constraints if provided
        if foreign_keys:
            column_defs.extend(foreign_keys)

        # Create the SQL statement
        columns_sql = ",\n                ".join(column_defs)
        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {columns_sql}
            )
        """

        try:
            self.execute_update(create_sql)
            print(f"Table '{table_name}' created successfully")
        except sqlite3.Error as e:
            print(f"Error creating table '{table_name}': {e}")
            raise

    def create_simple_table(self, table_name: str, column_specs: List[str]) -> None:
        """
        Create a simple table with basic column specifications.

        Args:
            table_name (str): Name of the table to create
            column_specs (List[str]): List of column specifications

        Example:
            db.create_simple_table("categories", [
                "id INTEGER PRIMARY KEY AUTOINCREMENT",
                "name TEXT NOT NULL UNIQUE",
                "description TEXT",
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
            ])
        """
        columns_sql = ",\n                ".join(column_specs)
        create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_name} (
                {columns_sql}
            )
        """

        try:
            self.execute_update(create_sql)
            print(f"Table '{table_name}' created successfully")
        except sqlite3.Error as e:
            print(f"Error creating table '{table_name}': {e}")
            raise

    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()


# Example usage functions
def insert_sample_data(db: Database) -> None:
    """Insert some sample data into the database."""
    # Insert users
    users = [
        ("john_doe", "<EMAIL>"),
        ("jane_smith", "<EMAIL>"),
        ("bob_wilson", "<EMAIL>")
    ]
    
    user_query = "INSERT INTO users (username, email) VALUES (?, ?)"
    db.execute_many(user_query, users)
    
    # Insert posts
    posts = [
        (1, "First Post", "This is my first post!"),
        (1, "Another Post", "Here's another post by John."),
        (2, "Jane's Post", "Hello from Jane!"),
        (3, "Bob's Thoughts", "Some thoughts from Bob.")
    ]
    
    post_query = "INSERT INTO posts (user_id, title, content) VALUES (?, ?, ?)"
    db.execute_many(post_query, posts)
    
    print("Sample data inserted successfully")


def query_sample_data(db: Database) -> None:
    """Query and display sample data."""
    # Get all users
    users = db.execute_query("SELECT * FROM users")
    print("\n--- Users ---")
    for user in users:
        print(f"ID: {user['id']}, Username: {user['username']}, Email: {user['email']}")
    
    # Get posts with user information
    posts_query = """
    SELECT p.id, p.title, p.content, u.username, p.created_at
    FROM posts p
    JOIN users u ON p.user_id = u.id
    ORDER BY p.created_at DESC
    """
    posts = db.execute_query(posts_query)
    print("\n--- Posts ---")
    for post in posts:
        print(f"'{post['title']}' by {post['username']}")
        print(f"Content: {post['content']}")
        print(f"Created: {post['created_at']}\n")


def create_revs_table(db: Database) -> None:
    """Create the revs table with your specified columns."""
    db.create_simple_table("revs", [
        "id INTEGER PRIMARY KEY AUTOINCREMENT",
        "reviews TEXT",
        "text TEXT",
        "agent_reply TEXT",
        "edited_reviews TEXT",
        "modified_or_not TEXT",
        "status TEXT",
        "secondary_class TEXT",
        "rating REAL",
        "is_deleted INTEGER DEFAULT 0",
        "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
        "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
    ])


def add_edited_reviews_column(db: Database) -> None:
    """Add edited_reviews column to existing revs table if it doesn't exist."""
    try:
        # Check if column exists by trying to select it
        db.execute_query("SELECT edited_reviews FROM revs LIMIT 1")
        print("edited_reviews column already exists")
    except:
        # Column doesn't exist, add it
        try:
            db.execute_update("ALTER TABLE revs ADD COLUMN edited_reviews TEXT")
            print("Added edited_reviews column to revs table")
        except Exception as e:
            print(f"Error adding edited_reviews column: {e}")


def add_new_columns(db: Database) -> None:
    """Add new columns to existing revs table if they don't exist."""
    columns_to_add = [
        ("secondary_class", "TEXT"),
        ("rating", "REAL"),
        ("is_deleted", "INTEGER DEFAULT 0")
    ]

    for column_name, column_type in columns_to_add:
        try:
            # Check if column exists by trying to select it
            db.execute_query(f"SELECT {column_name} FROM revs LIMIT 1")
            print(f"{column_name} column already exists")
        except:
            # Column doesn't exist, add it
            try:
                db.execute_update(f"ALTER TABLE revs ADD COLUMN {column_name} {column_type}")
                print(f"Added {column_name} column to revs table")
            except Exception as e:
                print(f"Error adding {column_name} column: {e}")


def insert_sample_revs_data(db: Database) -> None:
    """Insert some sample data into the revs table."""
    sample_revs = [
        ("Great product!", "Customer loves the quality", "Thank you for your feedback!", "not modified", "active"),
        ("Could be better", "Customer suggests improvements", "We appreciate your suggestions", "modified", "pending"),
        ("Excellent service", "Customer praises support team", "We're glad you had a great experience", "not modified", "active"),
        ("Delivery was slow", "Customer complains about shipping", "We apologize and will improve delivery times", "modified", "resolved")
    ]

    revs_query = "INSERT INTO revs (reviews, text, agent_reply, modified_or_not, status) VALUES (?, ?, ?, ?, ?)"
    db.execute_many(revs_query, sample_revs)
    print("Sample revs data inserted successfully")


def query_revs_data(db: Database) -> None:
    """Query and display revs data."""
    revs = db.execute_query("SELECT * FROM revs ORDER BY created_at DESC")
    print("\n--- Reviews Data ---")
    for rev in revs:
        print(f"ID: {rev['id']}")
        print(f"Review: {rev['reviews']}")
        print(f"Text: {rev['text']}")
        print(f"Agent Reply: {rev['agent_reply']}")
        print(f"Modified: {rev['modified_or_not']}")
        print(f"Status: {rev['status']}")
        print(f"Created: {rev['created_at']}")
        print("-" * 50)


if __name__ == "__main__":
    # Example usage
    with Database("app.db") as db:
        # Create original tables
        db.create_tables()

        # Create your new revs table
        create_revs_table(db)

        # Check if we need to insert sample data for users/posts
        users = db.execute_query("SELECT COUNT(*) as count FROM users")
        if users[0]['count'] == 0:
            insert_sample_data(db)

        # Check if we need to insert sample data for revs
        revs = db.execute_query("SELECT COUNT(*) as count FROM revs")
        if revs[0]['count'] == 0:
            insert_sample_revs_data(db)

        # Query and display all data
        query_sample_data(db)
        query_revs_data(db)
