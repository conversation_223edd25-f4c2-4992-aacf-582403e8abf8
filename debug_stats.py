#!/usr/bin/env python3
"""
Debug script to test the statistics functionality.
"""

import pandas as pd
from database import Database

def test_statistics():
    """Test the statistics function step by step."""
    
    print("=== DEBUGGING STATISTICS FUNCTION ===\n")
    
    try:
        # Step 1: Load reviews
        print("1. Loading reviews from database...")
        with Database("app.db") as db:
            reviews = db.execute_query("SELECT * FROM revs ORDER BY id")
        
        print(f"   ✅ Found {len(reviews)} reviews")
        
        if not reviews:
            print("   ❌ No reviews found! This is the problem.")
            return
        
        # Step 2: Convert to DataFrame
        print("\n2. Converting to DataFrame...")
        df = pd.DataFrame(reviews)
        print(f"   ✅ DataFrame created with shape: {df.shape}")
        print(f"   ✅ Columns: {list(df.columns)}")
        
        # Step 3: Check date conversion
        print("\n3. Testing date conversion...")
        try:
            df['created_at'] = pd.to_datetime(df['created_at'])
            df['updated_at'] = pd.to_datetime(df['updated_at'])
            print("   ✅ Date conversion successful")
        except Exception as e:
            print(f"   ❌ Date conversion failed: {e}")
            return
        
        # Step 4: Calculate basic KPIs
        print("\n4. Calculating KPIs...")
        total_reviews = len(df)
        modified_reviews = len(df[df['modified_or_not'] == 'modified'])
        active_reviews = len(df[df['status'] == 'active'])
        pending_reviews = len(df[df['status'] == 'pending'])
        resolved_reviews = len(df[df['status'] == 'resolved'])
        
        print(f"   ✅ Total reviews: {total_reviews}")
        print(f"   ✅ Modified reviews: {modified_reviews}")
        print(f"   ✅ Active reviews: {active_reviews}")
        print(f"   ✅ Pending reviews: {pending_reviews}")
        print(f"   ✅ Resolved reviews: {resolved_reviews}")
        
        # Step 5: Test percentages
        print("\n5. Testing percentage calculations...")
        modification_rate = (modified_reviews / total_reviews * 100) if total_reviews > 0 else 0
        resolution_rate = (resolved_reviews / total_reviews * 100) if total_reviews > 0 else 0
        active_rate = (active_reviews / total_reviews * 100) if total_reviews > 0 else 0
        
        print(f"   ✅ Modification rate: {modification_rate:.1f}%")
        print(f"   ✅ Resolution rate: {resolution_rate:.1f}%")
        print(f"   ✅ Active rate: {active_rate:.1f}%")
        
        # Step 6: Test edited reviews
        print("\n6. Testing edited reviews...")
        reviews_with_edits = len(df[df['edited_reviews'].notna() & (df['edited_reviews'] != '')])
        edit_rate = (reviews_with_edits / total_reviews * 100) if total_reviews > 0 else 0
        print(f"   ✅ Reviews with edits: {reviews_with_edits}")
        print(f"   ✅ Edit rate: {edit_rate:.1f}%")
        
        # Step 7: Test charts data
        print("\n7. Testing chart data...")
        status_counts = df['status'].value_counts()
        mod_counts = df['modified_or_not'].value_counts()
        
        print(f"   ✅ Status counts: {dict(status_counts)}")
        print(f"   ✅ Modification counts: {dict(mod_counts)}")
        
        # Step 8: Test time-based analysis
        print("\n8. Testing time-based analysis...")
        df['date'] = df['created_at'].dt.date
        daily_reviews = df.groupby('date').size()
        print(f"   ✅ Daily reviews: {len(daily_reviews)} unique dates")
        
        df['hour'] = df['created_at'].dt.hour
        hourly_reviews = df.groupby('hour').size()
        print(f"   ✅ Hourly reviews: {len(hourly_reviews)} unique hours")
        
        print("\n✅ ALL TESTS PASSED! Statistics function should work.")
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_statistics()
